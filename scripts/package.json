{"name": "@spritely/scripts", "version": "1.0.0", "type": "module", "private": true, "imports": {"#config/*": "./config/*.js", "#utils/*": "./utils/*.js", "#tiers/*": "./tiers/*.js", "#constants": "./config/constants.js", "#database": "./config/database.js", "#helpers": "./utils/helpers.js", "#progress": "./utils/progress.js"}, "description": "Modular healthcare data seeding system for Spritely", "main": "seed.js", "scripts": {"seed": "node seed.js", "seed:tier1": "node tiers/tier1-foundation.js", "seed:tier2": "node tiers/tier2-organizations.js", "seed:tier3": "node tiers/tier3-departments.js", "seed:tier4": "node tiers/tier4-users.js", "seed:tier5": "node tiers/tier5-clinical.js", "seed:tier6": "node tiers/tier6-administrative.js", "seed:tier7": "node tiers/tier7-communication.js"}, "dependencies": {"@faker-js/faker": "^9.8.0", "@supabase/supabase-js": "^2.39.0", "dotenv": "^16.3.1"}, "keywords": ["healthcare", "seeding", "faker", "supabase", "modular"], "author": "Spritely Team"}