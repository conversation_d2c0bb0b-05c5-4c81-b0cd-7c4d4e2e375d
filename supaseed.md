# SupaSeed: Domain-Agnostic Supabase Seed Generator

## Overview
A TypeScript-first, domain-agnostic seed data generator that reads Supabase generated types and automatically creates realistic, relationship-aware test data for any schema.

## Core Architecture

### 1. Type System Foundation
```typescript
// Core types extracted from any Supabase generated-types.ts
type DatabaseSchema = {
  public: {
    Tables: Record<string, {
      Row: Record<string, any>;
      Insert: Record<string, any>;
      Update: Record<string, any>;
      Relationships: Array<{
        foreignKeyName: string;
        columns: string[];
        referencedRelation: string;
        referencedColumns: string[];
        isOneToOne: boolean;
      }>;
    }>;
    Enums: Record<string, string>;
  };
};
```

### 2. Schema Introspection Engine
```typescript
class SchemaAnalyzer<TDatabase extends DatabaseSchema> {
  // Extract all table names and their dependencies
  getTableDependencyGraph(): Map<string, string[]>;
  
  // Get column metadata for intelligent data generation
  getColumnMetadata(tableName: string): ColumnMetadata[];
  
  // Find foreign key relationships
  getForeignKeyRelationships(tableName: string): ForeignKeyRelation[];
  
  // Get enum values for type-safe generation
  getEnumValues(enumName: string): string[];
}

interface ColumnMetadata {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'Json' | 'enum';
  nullable: boolean;
  enumType?: string;
  foreignKey?: {
    table: string;
    column: string;
  };
}
```

### 3. Smart Data Generation Engine
```typescript
interface DataGenerator {
  // Generate data based on column name patterns
  generateByColumnName(columnName: string, type: string): any;
  
  // Generate data based on table context
  generateByTableContext(tableName: string, columnName: string, type: string): any;
  
  // Generate enum values
  generateEnumValue(enumValues: string[]): string;
  
  // Generate JSON/JSONB data
  generateJsonData(columnName: string): Record<string, any>;
}

class IntelligentDataGenerator implements DataGenerator {
  private patterns = {
    // Email patterns
    email: /^(email|contact_email|user_email)$/i,
    
    // Name patterns
    firstName: /^(first_name|fname|given_name)$/i,
    lastName: /^(last_name|lname|surname|family_name)$/i,
    fullName: /^(name|full_name|display_name)$/i,
    
    // Address patterns
    address: /^(address|street|street_address)$/i,
    city: /^(city|town)$/i,
    state: /^(state|province|region)$/i,
    zipCode: /^(zip|zip_code|postal_code)$/i,
    
    // Phone patterns
    phone: /^(phone|telephone|mobile|cell)$/i,
    
    // Date patterns
    birthDate: /^(birth_date|date_of_birth|dob)$/i,
    createdAt: /^(created_at|created_date)$/i,
    updatedAt: /^(updated_at|modified_at)$/i,
    
    // Medical patterns (domain-specific)
    medicalRecord: /^(mrn|medical_record_number)$/i,
    diagnosis: /^(diagnosis|condition)$/i,
    medication: /^(medication|drug|prescription)$/i,
    
    // Business patterns
    companyName: /^(company|organization|business)_name$/i,
    website: /^(website|url|homepage)$/i,
    
    // Financial patterns
    amount: /^(amount|price|cost|fee)$/i,
    currency: /^(currency|currency_code)$/i,
  };
}
```

### 4. Relationship Resolution Engine
```typescript
class RelationshipResolver<TDatabase extends DatabaseSchema> {
  // Build dependency graph for correct insertion order
  buildInsertionOrder(tables: string[]): string[];
  
  // Resolve foreign key values during generation
  resolveForeignKey(
    tableName: string, 
    columnName: string, 
    referencedTable: string,
    generatedData: Map<string, any[]>
  ): string;
  
  // Handle circular dependencies
  handleCircularDependencies(graph: Map<string, string[]>): string[];
}
```

### 5. Configuration System
```typescript
interface SeedConfig {
  // Record counts per table
  recordCounts: Record<string, number | { min: number; max: number }>;
  
  // Custom data generators per column
  columnOverrides: Record<string, (faker: Faker) => any>;
  
  // Relationship constraints
  relationships: {
    // Ensure certain relationships exist
    required: Array<{
      parentTable: string;
      childTable: string;
      ratio: number; // children per parent
    }>;
  };
  
  // Output configuration
  output: {
    format: 'sql' | 'json';
    directory: string;
    fileNaming: 'single' | 'per-table' | 'by-tier';
  };
  
  // Domain-specific generators
  dataGenerators: DataGenerator[];
}
```

### 6. Core Generator Class
```typescript
class SupaSeedGenerator<TDatabase extends DatabaseSchema> {
  constructor(
    private schema: TDatabase,
    private config: SeedConfig
  ) {}
  
  // Main generation method
  async generate(): Promise<void> {
    const analyzer = new SchemaAnalyzer(this.schema);
    const resolver = new RelationshipResolver(this.schema);
    const dataGen = new IntelligentDataGenerator();
    
    // 1. Analyze schema
    const tables = analyzer.getTableDependencyGraph();
    const insertionOrder = resolver.buildInsertionOrder(Array.from(tables.keys()));
    
    // 2. Generate data in dependency order
    const generatedData = new Map<string, any[]>();
    
    for (const tableName of insertionOrder) {
      const tableData = await this.generateTableData(
        tableName, 
        analyzer, 
        resolver, 
        dataGen, 
        generatedData
      );
      generatedData.set(tableName, tableData);
    }
    
    // 3. Output SQL files
    await this.outputSQLFiles(generatedData);
  }
  
  private async generateTableData(
    tableName: string,
    analyzer: SchemaAnalyzer<TDatabase>,
    resolver: RelationshipResolver<TDatabase>,
    dataGen: IntelligentDataGenerator,
    existingData: Map<string, any[]>
  ): Promise<any[]> {
    // Implementation details...
  }
}
```

## Implementation Plan

### Phase 1: Core Infrastructure
1. **Type extraction utilities** - Parse Supabase generated types
2. **Schema analyzer** - Extract tables, columns, relationships
3. **Basic data generator** - Column name pattern matching
4. **Relationship resolver** - Dependency graph and FK resolution

### Phase 2: Smart Generation
1. **Intelligent data patterns** - Expand pattern matching
2. **JSON/JSONB handling** - Smart object generation
3. **Enum support** - Type-safe enum value generation
4. **Configuration system** - Flexible record counts and overrides

### Phase 3: Output & Integration
1. **SQL file generation** - Optimized INSERT statements
2. **File organization** - Multiple output formats
3. **CLI interface** - Easy command-line usage
4. **Integration helpers** - Package.json scripts

### Phase 4: Domain Extensions
1. **Healthcare generator** - Medical-specific data patterns
2. **E-commerce generator** - Product/order patterns
3. **Social media generator** - User/content patterns
4. **Plugin system** - Custom domain generators

## File Structure
```
scripts/
├── supaseed/
│   ├── core/
│   │   ├── schema-analyzer.ts
│   │   ├── relationship-resolver.ts
│   │   ├── data-generator.ts
│   │   └── sql-generator.ts
│   ├── generators/
│   │   ├── base-generator.ts
│   │   ├── healthcare-generator.ts
│   │   └── generic-generator.ts
│   ├── types/
│   │   ├── schema.ts
│   │   ├── config.ts
│   │   └── generator.ts
│   ├── utils/
│   │   ├── type-helpers.ts
│   │   └── faker-extensions.ts
│   └── index.ts
├── supaseed.config.ts
└── generate-seeds.ts
```

## Usage Example
```typescript
// supaseed.config.ts
import type { Database } from './packages/supabase-types/src/generated-types';
import { HealthcareDataGenerator } from './scripts/supaseed/generators/healthcare-generator';

export const config: SeedConfig = {
  recordCounts: {
    organizations: 5,
    patients: { min: 20, max: 50 },
    appointments: { min: 100, max: 200 }
  },
  dataGenerators: [new HealthcareDataGenerator()],
  output: {
    format: 'sql',
    directory: 'supabase/seeds/environments/development',
    fileNaming: 'by-tier'
  }
};

// generate-seeds.ts
import { SupaSeedGenerator } from './scripts/supaseed';
import type { Database } from './packages/supabase-types/src/generated-types';
import { config } from './supaseed.config';

const generator = new SupaSeedGenerator<Database>(config);
await generator.generate();
```

## Technical Requirements

### Dependencies
- `@faker-js/faker` - Data generation
- `typescript` - Type safety
- Node.js built-ins: `fs`, `path`

### Type Safety
- Full TypeScript support with generated Supabase types
- Generic constraints for schema validation
- Compile-time relationship validation

### Performance
- Efficient dependency resolution (topological sort)
- Batched SQL generation
- Memory-efficient for large datasets

### Extensibility
- Plugin architecture for domain-specific generators
- Configurable data patterns
- Custom column override system

## Success Criteria
1. ✅ Works with ANY Supabase schema without modification
2. ✅ Generates realistic, relationship-consistent data
3. ✅ Type-safe throughout the entire pipeline
4. ✅ Produces optimized SQL for fast seeding
5. ✅ Easily configurable for different use cases
6. ✅ Extensible for domain-specific needs

This system will be the definitive solution for Supabase seed data generation across any domain.
