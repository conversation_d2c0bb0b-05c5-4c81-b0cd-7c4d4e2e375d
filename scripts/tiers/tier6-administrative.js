import { supabase } from '#database';
import { logProgress, randomElement, updateProgress } from '#helpers';
import { faker } from '@faker-js/faker';

// Tier 6: Administrative Operations
export async function generateTier6Administrative() {
  console.log('💼 Generating Tier 6: Administrative Operations...');
  
  try {
    // Fetch required data from previous tiers
    const { data: organizations, error: orgsError } = await supabase
      .from('organizations')
      .select('*');
    
    if (orgsError) throw orgsError;
    if (!organizations || organizations.length === 0) {
      throw new Error('No organizations found. Please run Tier 2 first.');
    }
    
    const { data: appointments, error: appointmentsError } = await supabase
      .from('appointments')
      .select('*');
    
    if (appointmentsError) throw appointmentsError;
    if (!appointments || appointments.length === 0) {
      throw new Error('No appointments found. Please run Tier 5 first.');
    }
    
    const { data: medicalRecords, error: recordsError } = await supabase
      .from('medical_records')
      .select('*');
    
    if (recordsError) throw recordsError;
    if (!medicalRecords || medicalRecords.length === 0) {
      throw new Error('No medical records found. Please run Tier 5 first.');
    }
    
    const { data: insuranceProviders, error: insuranceError } = await supabase
      .from('insurance_providers')
      .select('*');
    
    if (insuranceError) throw insuranceError;
    if (!insuranceProviders || insuranceProviders.length === 0) {
      throw new Error('No insurance providers found. Please run Tier 1 first.');
    }
    
    const { data: patients, error: patientsError } = await supabase
      .from('patients')
      .select('*');
    
    if (patientsError) throw patientsError;
    if (!patients || patients.length === 0) {
      throw new Error('No patients found. Please run Tier 4 first.');
    }
    
    const results = {};
    
    // Billing Records
    results.billingRecords = await seedBillingRecords(appointments, medicalRecords, patients);
    
    // Insurance Claims
    results.insuranceClaims = await seedInsuranceClaims(results.billingRecords, insuranceProviders, patients);
    
    // Payments
    results.payments = await seedPayments(results.insuranceClaims, results.billingRecords, patients);
    
    // Patient Invoices
    results.patientInvoices = await seedPatientInvoices(results.billingRecords, results.payments, patients);
    
    console.log('✅ Tier 6 completed successfully');
    return results;
  } catch (error) {
    console.error('❌ Error in Tier 6:', error);
    throw error;
  }
}

async function seedBillingRecords(appointments, medicalRecords, patients) {
  updateProgress('Billing Records');
  
  const billingRecords = [];
  
  // Create billing records for completed appointments
  const completedAppointments = appointments.filter(a => a.status === 'completed');
  
  for (const appointment of completedAppointments) {
    const patient = patients.find(p => p.id === appointment.patient_id);
    const medicalRecord = medicalRecords.find(r => r.appointment_id === appointment.id);
    
    // Generate billing codes for this encounter
    const billingCodes = generateBillingCodes(appointment.type, medicalRecord);
    const totalCharges = calculateTotalCharges(billingCodes);
    
    billingRecords.push({
      organization_id: appointment.organization_id,
      patient_id: appointment.patient_id,
      provider_id: appointment.provider_id,
      appointment_id: appointment.id,
      medical_record_id: medicalRecord?.id || null,
      billing_date: appointment.appointment_date,
      service_date: appointment.appointment_date,
      billing_codes: billingCodes,
      total_charges: totalCharges,
      patient_responsibility: calculatePatientResponsibility(totalCharges, patient.insurance),
      insurance_responsibility: calculateInsuranceResponsibility(totalCharges, patient.insurance),
      status: generateBillingStatus(),
      billing_notes: generateBillingNotes(),
      facility_id: appointment.department_id, // Assuming department maps to facility
      diagnosis_codes: medicalRecord?.diagnoses.map(d => d.code) || [],
      procedure_codes: billingCodes.filter(c => c.type === 'CPT').map(c => c.code)
    });
  }

  const { data, error } = await supabase.from('billing_records').insert(billingRecords).select();
  if (error) throw error;
  
  logProgress(`Created ${billingRecords.length} billing records`);
  return data;
}

async function seedInsuranceClaims(billingRecords, insuranceProviders, patients) {
  updateProgress('Insurance Claims');
  
  const insuranceClaims = [];
  
  for (const billingRecord of billingRecords) {
    const patient = patients.find(p => p.id === billingRecord.patient_id);
    
    // Primary insurance claim
    insuranceClaims.push({
      billing_record_id: billingRecord.id,
      patient_id: billingRecord.patient_id,
      insurance_provider_id: patient.insurance.primary.provider_id,
      claim_number: generateClaimNumber(),
      claim_type: 'primary',
      submission_date: addDays(billingRecord.billing_date, faker.number.int({ min: 1, max: 7 })),
      total_billed: billingRecord.total_charges,
      total_allowed: calculateAllowedAmount(billingRecord.total_charges),
      total_paid: 0, // Will be updated when payment is processed
      patient_copay: patient.insurance.primary.copay,
      deductible_applied: calculateDeductibleApplied(billingRecord.total_charges, patient.insurance.primary),
      status: generateClaimStatus(),
      processing_notes: generateProcessingNotes(),
      member_id: patient.insurance.primary.member_id,
      group_number: patient.insurance.primary.group_number,
      authorization_number: billingRecord.billing_codes.some(c => c.code === '99215') ? faker.string.alphanumeric(10) : null
    });
    
    // Secondary insurance claim (if patient has secondary)
    if (patient.insurance.secondary) {
      insuranceClaims.push({
        billing_record_id: billingRecord.id,
        patient_id: billingRecord.patient_id,
        insurance_provider_id: patient.insurance.secondary.provider_id,
        claim_number: generateClaimNumber(),
        claim_type: 'secondary',
        submission_date: addDays(billingRecord.billing_date, faker.number.int({ min: 7, max: 14 })),
        total_billed: billingRecord.total_charges,
        total_allowed: calculateAllowedAmount(billingRecord.total_charges * 0.8), // Secondary typically pays less
        total_paid: 0,
        patient_copay: 0,
        deductible_applied: 0,
        status: generateClaimStatus(),
        processing_notes: generateProcessingNotes(),
        member_id: patient.insurance.secondary.member_id,
        group_number: patient.insurance.secondary.group_number,
        authorization_number: null
      });
    }
  }

  const { data, error } = await supabase.from('insurance_claims').insert(insuranceClaims).select();
  if (error) throw error;
  
  logProgress(`Created ${insuranceClaims.length} insurance claims`);
  return data;
}

async function seedPayments(insuranceClaims, billingRecords, patients) {
  updateProgress('Payments');
  
  const payments = [];
  
  for (const claim of insuranceClaims) {
    // Insurance payments for processed claims
    if (claim.status === 'paid' || claim.status === 'partially_paid') {
      const paymentAmount = claim.status === 'paid' ? claim.total_allowed : claim.total_allowed * 0.8;
      
      payments.push({
        billing_record_id: claim.billing_record_id,
        insurance_claim_id: claim.id,
        patient_id: claim.patient_id,
        payment_type: 'insurance',
        payment_method: 'electronic',
        payment_amount: paymentAmount,
        payment_date: addDays(claim.submission_date, faker.number.int({ min: 14, max: 45 })),
        reference_number: faker.string.alphanumeric(12),
        processed_by: faker.person.fullName(),
        payment_notes: generatePaymentNotes('insurance'),
        check_number: claim.claim_type === 'primary' ? faker.string.numeric(8) : null,
        remittance_advice: generateRemittanceAdvice(claim)
      });
      
      // Update the claim with payment amount
      claim.total_paid = paymentAmount;
    }
    
    // Patient payments (copays, deductibles, etc.)
    if (faker.datatype.boolean({ probability: 0.7 })) {
      const billingRecord = billingRecords.find(br => br.id === claim.billing_record_id);
      const patient = patients.find(p => p.id === claim.patient_id);
      const patientAmount = calculatePatientPaymentAmount(claim, billingRecord, patient);
      
      if (patientAmount > 0) {
        payments.push({
          billing_record_id: claim.billing_record_id,
          insurance_claim_id: null,
          patient_id: claim.patient_id,
          payment_type: 'patient',
          payment_method: randomElement(['credit_card', 'debit_card', 'cash', 'check', 'online']),
          payment_amount: patientAmount,
          payment_date: addDays(claim.submission_date, faker.number.int({ min: 7, max: 30 })),
          reference_number: faker.string.alphanumeric(10),
          processed_by: faker.person.fullName(),
          payment_notes: generatePaymentNotes('patient'),
          check_number: null,
          remittance_advice: null
        });
      }
    }
  }

  const { data, error } = await supabase.from('payments').insert(payments).select();
  if (error) throw error;
  
  logProgress(`Created ${payments.length} payments`);
  return data;
}

async function seedPatientInvoices(billingRecords, payments, patients) {
  updateProgress('Patient Invoices');
  
  const patientInvoices = [];
  
  for (const billingRecord of billingRecords) {
    const patient = patients.find(p => p.id === billingRecord.patient_id);
    const relatedPayments = payments.filter(p => p.billing_record_id === billingRecord.id);
    const totalPayments = relatedPayments.reduce((sum, p) => sum + p.payment_amount, 0);
    const balanceDue = Math.max(0, billingRecord.patient_responsibility - totalPayments);
    
    patientInvoices.push({
      billing_record_id: billingRecord.id,
      patient_id: billingRecord.patient_id,
      invoice_number: generateInvoiceNumber(),
      invoice_date: addDays(billingRecord.billing_date, faker.number.int({ min: 7, max: 21 })),
      due_date: addDays(billingRecord.billing_date, faker.number.int({ min: 30, max: 60 })),
      total_charges: billingRecord.total_charges,
      insurance_payments: relatedPayments.filter(p => p.payment_type === 'insurance').reduce((sum, p) => sum + p.payment_amount, 0),
      patient_payments: relatedPayments.filter(p => p.payment_type === 'patient').reduce((sum, p) => sum + p.payment_amount, 0),
      adjustments: generateAdjustments(billingRecord.total_charges),
      balance_due: balanceDue,
      status: generateInvoiceStatus(balanceDue),
      aging_category: generateAgingCategory(billingRecord.billing_date),
      last_statement_date: addDays(billingRecord.billing_date, faker.number.int({ min: 14, max: 45 })),
      payment_plan: balanceDue > 500 ? generatePaymentPlan(balanceDue) : null,
      collection_status: balanceDue > 1000 && faker.datatype.boolean({ probability: 0.3 }) ? generateCollectionStatus() : null,
      invoice_details: {
        service_description: generateServiceDescription(billingRecord),
        itemized_charges: generateItemizedCharges(billingRecord.billing_codes),
        insurance_explanation: generateInsuranceExplanation(patient.insurance)
      }
    });
  }

  const { data, error } = await supabase.from('patient_invoices').insert(patientInvoices).select();
  if (error) throw error;
  
  logProgress(`Created ${patientInvoices.length} patient invoices`);
  return data;
}

// Helper functions
function generateBillingCodes(appointmentType, medicalRecord) {
  const codes = [];
  
  // Base visit code
  const visitCodes = {
    'Annual Physical': { code: '99396', description: 'Preventive medicine evaluation', amount: 250 },
    'Follow-up': { code: '99213', description: 'Office visit, established patient, level 3', amount: 150 },
    'Consultation': { code: '99243', description: 'Office consultation, level 3', amount: 200 },
    'Emergency Visit': { code: '99284', description: 'Emergency department visit, level 4', amount: 400 }
  };
  
  const baseCode = visitCodes[appointmentType] || visitCodes['Follow-up'];
  codes.push({ ...baseCode, type: 'CPT' });
  
  // Add additional procedures if any
  if (medicalRecord?.procedures) {
    for (const procedure of medicalRecord.procedures) {
      codes.push({
        code: procedure.code,
        description: procedure.description,
        type: 'CPT',
        amount: faker.number.int({ min: 50, max: 300 })
      });
    }
  }
  
  // Add diagnosis codes
  if (medicalRecord?.diagnoses) {
    for (const diagnosis of medicalRecord.diagnoses) {
      codes.push({
        code: diagnosis.code,
        description: diagnosis.description,
        type: 'ICD-10',
        amount: 0 // Diagnosis codes don't have charges
      });
    }
  }
  
  return codes;
}

function calculateTotalCharges(billingCodes) {
  return billingCodes.reduce((total, code) => total + (code.amount || 0), 0);
}

function calculatePatientResponsibility(totalCharges, insurance) {
  const copay = insurance.primary.copay || 0;
  const deductible = Math.min(totalCharges * 0.1, insurance.primary.deductible || 0);
  return copay + deductible;
}

function calculateInsuranceResponsibility(totalCharges, insurance) {
  const patientResponsibility = calculatePatientResponsibility(totalCharges, insurance);
  return Math.max(0, totalCharges - patientResponsibility);
}

function generateBillingStatus() {
  return randomElement(['pending', 'submitted', 'processed', 'paid', 'denied']);
}

function generateBillingNotes() {
  if (faker.datatype.boolean({ probability: 0.3 })) {
    return faker.lorem.sentence();
  }
  return null;
}

function generateClaimNumber() {
  return `CLM${faker.string.numeric(10)}`;
}

function calculateAllowedAmount(billedAmount) {
  // Insurance typically allows 70-90% of billed amount
  const allowanceRate = faker.number.float({ min: 0.7, max: 0.9, precision: 0.01 });
  return Math.round(billedAmount * allowanceRate * 100) / 100;
}

function calculateDeductibleApplied(totalCharges, insurance) {
  if (faker.datatype.boolean({ probability: 0.3 })) {
    return Math.min(totalCharges * 0.1, insurance.deductible || 0);
  }
  return 0;
}

function generateClaimStatus() {
  return randomElement(['pending', 'processing', 'paid', 'partially_paid', 'denied', 'appealed']);
}

function generateProcessingNotes() {
  const notes = [
    'Claim processed successfully',
    'Prior authorization verified',
    'Benefits verified',
    'Pending additional documentation',
    'Requires medical review'
  ];
  
  return randomElement(notes);
}

function calculatePatientPaymentAmount(claim, billingRecord, patient) {
  const copay = patient.insurance.primary.copay || 0;
  const deductible = calculateDeductibleApplied(billingRecord.total_charges, patient.insurance.primary);
  return copay + deductible;
}

function generatePaymentNotes(paymentType) {
  if (paymentType === 'insurance') {
    return randomElement([
      'Electronic funds transfer',
      'Check payment received',
      'Claim processed per contract',
      'Payment made per EOB'
    ]);
  } else {
    return randomElement([
      'Patient payment received',
      'Copay collected at time of service',
      'Online payment processed',
      'Payment plan installment'
    ]);
  }
}

function generateRemittanceAdvice(claim) {
  return {
    eob_number: faker.string.alphanumeric(12),
    processing_date: claim.submission_date,
    allowed_amount: claim.total_allowed,
    paid_amount: claim.total_paid,
    denial_reason: claim.status === 'denied' ? 'Services not covered' : null
  };
}

function generateInvoiceNumber() {
  return `INV${faker.string.numeric(8)}`;
}

function generateAdjustments(totalCharges) {
  if (faker.datatype.boolean({ probability: 0.2 })) {
    return faker.number.float({ min: -100, max: 0, precision: 0.01 });
  }
  return 0;
}

function generateInvoiceStatus(balanceDue) {
  if (balanceDue === 0) return 'paid';
  if (balanceDue < 50) return 'pending';
  return randomElement(['pending', 'overdue', 'collections']);
}

function generateAgingCategory(billingDate) {
  const daysSince = Math.floor((new Date() - new Date(billingDate)) / (1000 * 60 * 60 * 24));
  
  if (daysSince <= 30) return '0-30 days';
  if (daysSince <= 60) return '31-60 days';
  if (daysSince <= 90) return '61-90 days';
  if (daysSince <= 120) return '91-120 days';
  return '120+ days';
}

function generatePaymentPlan(balanceDue) {
  const monthlyPayment = Math.round(balanceDue / faker.number.int({ min: 3, max: 12 }));
  return {
    monthly_payment: monthlyPayment,
    number_of_payments: Math.ceil(balanceDue / monthlyPayment),
    start_date: faker.date.future({ days: 30 }).toISOString().split('T')[0],
    status: 'active'
  };
}

function generateCollectionStatus() {
  return randomElement(['pre_collection', 'first_notice', 'final_notice', 'external_collection']);
}

function generateServiceDescription(billingRecord) {
  return `Medical services provided on ${billingRecord.service_date}`;
}

function generateItemizedCharges(billingCodes) {
  return billingCodes.filter(c => c.amount > 0).map(code => ({
    code: code.code,
    description: code.description,
    amount: code.amount
  }));
}

function generateInsuranceExplanation(insurance) {
  return {
    primary_insurance: insurance.primary.plan_name,
    member_id: insurance.primary.member_id,
    explanation: 'Benefits applied per insurance contract terms'
  };
}

function addDays(dateString, days) {
  const date = new Date(dateString);
  date.setDate(date.getDate() + days);
  return date.toISOString().split('T')[0];
} 