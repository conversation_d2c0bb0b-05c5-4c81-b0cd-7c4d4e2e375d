import { COMMON_ALLERGIES, CONFIG, MEDICAL_SPECIALTIES } from '#constants';
import { supabase } from '#database';
import { calculateAge, logProgress, randomElement, updateProgress } from '#helpers';
import { faker } from '@faker-js/faker';

// Tier 4: Healthcare Providers & Patients
export async function generateTier4Users() {
  console.log('👥 Generating Tier 4: Healthcare Providers & Patients...');

  try {
    // Fetch required data from previous tiers
    const { data: organizations, error: orgsError } = await supabase
      .from('organizations')
      .select('*');

    if (orgsError) throw orgsError;
    if (!organizations || organizations.length === 0) {
      throw new Error('No organizations found. Please run Tier 2 first.');
    }

    const { data: departments, error: deptsError } = await supabase
      .from('departments')
      .select('*');

    if (deptsError) throw deptsError;
    if (!departments || departments.length === 0) {
      throw new Error('No departments found. Please run Tier 3 first.');
    }

    const { data: teams, error: teamsError } = await supabase
      .from('teams')
      .select('*');

    if (teamsError) throw teamsError;

    const { data: facilities, error: facilitiesError } = await supabase
      .from('facilities')
      .select('*');

    if (facilitiesError) throw facilitiesError;
    if (!facilities || facilities.length === 0) {
      throw new Error('No facilities found. Please run Tier 2 first.');
    }

    const { data: insuranceProviders, error: insuranceError } = await supabase
      .from('insurance_providers')
      .select('*');

    if (insuranceError) throw insuranceError;
    if (!insuranceProviders || insuranceProviders.length === 0) {
      throw new Error('No insurance providers found. Please run Tier 1 first.');
    }

    const results = {};

    // Healthcare Providers
    results.healthcareProviders = await seedHealthcareProviders(organizations, departments, teams, facilities);

    // Patients
    results.patients = await seedPatients(organizations, facilities, insuranceProviders);

    // Allergies (separate table with foreign key to patients)
    results.allergies = await seedAllergies(results.patients, results.healthcareProviders);

    console.log('✅ Tier 4 completed successfully');
    return results;
  } catch (error) {
    console.error('❌ Error in Tier 4:', error);
    throw error;
  }
}

async function seedHealthcareProviders(organizations, departments, teams, facilities) {
  updateProgress('Healthcare Providers');

  const providers = [];

  for (const org of organizations) {
    const providerCount = faker.number.int({ min: CONFIG.PROVIDERS_PER_ORG * 0.8, max: CONFIG.PROVIDERS_PER_ORG * 1.2 });
    const orgFacilities = facilities.filter(f => f.organization_id === org.id);
    const orgDepartments = departments.filter(d => {
      return orgFacilities.some(f => f.id === d.facility_id);
    });

    // Debug logging
    console.log(`Organization: ${org.name}`);
    console.log(`  Facilities: ${orgFacilities.length}`);
    console.log(`  Departments: ${orgDepartments.length}`);

    // Skip organizations with no departments
    if (orgDepartments.length === 0) {
      console.log(`  ⚠️ Skipping ${org.name} - no departments found`);
      continue;
    }

    for (let i = 0; i < providerCount; i++) {
      const department = randomElement(orgDepartments);
      const specialty = generateProviderSpecialty(department.type);
      const roleType = generateProviderRole(specialty);

      providers.push({
        organization_id: org.id,
        department_id: department.id,
        first_name: faker.person.firstName(),
        last_name: faker.person.lastName(),
        provider_type: roleType === 'physician' ? 'doctor' : roleType === 'nurse_practitioner' ? 'specialist' : roleType === 'registered_nurse' ? 'nurse' : 'nurse',
        specialization: specialty,
        license_number: generateLicenseNumber(roleType),
        role: roleType,
        specialties: [specialty],
        credentials: {
          employee_id: generateEmployeeId(org.name),
          title: generateProviderTitle(roleType, specialty),
          npi_number: roleType === 'physician' || roleType === 'nurse_practitioner' ? faker.string.numeric(10) : null,
          certifications: generateCredentials(roleType, specialty),
          contact_info: {
            email: faker.internet.email(),
            phone: faker.phone.number(),
            extension: faker.number.int({ min: 1000, max: 9999 }).toString()
          },
          employment: {
            hire_date: faker.date.past({ years: 10 }).toISOString().split('T')[0],
            employment_type: randomElement(['full-time', 'part-time', 'contract', 'per-diem']),
            shift_preference: randomElement(['day', 'evening', 'night', 'rotating']),
            fte: faker.number.float({ min: 0.5, max: 1.0, precision: 0.1 })
          },
          professional_info: {
            medical_school: generateMedicalSchool(roleType),
            residency: generateResidency(specialty),
            board_certified: faker.datatype.boolean({ probability: 0.8 }),
            years_experience: faker.number.int({ min: 1, max: 30 }),
            languages_spoken: generateLanguages()
          },
          status: randomElement(['active', 'active', 'active', 'active', 'on_leave'])
        },
        schedule_settings: generateProviderAvailability(),
        permissions: {
          can_prescribe: roleType === 'physician' || roleType === 'nurse_practitioner',
          can_order_labs: roleType === 'physician' || roleType === 'nurse_practitioner',
          can_access_all_patients: roleType === 'physician',
          can_modify_schedules: roleType === 'physician'
        }
      });
    }
  }

  if (providers.length === 0) {
    console.log('⚠️ No providers created - no valid organization/department combinations found');
    return [];
  }

  const { data, error } = await supabase.from('healthcare_providers').insert(providers).select();
  if (error) throw error;

  logProgress(`Created ${providers.length} healthcare providers`);
  return data;
}

async function seedPatients(organizations, facilities, insuranceProviders) {
  updateProgress('Patients');

  const patients = [];

  for (const org of organizations) {
    const patientCount = faker.number.int({ min: CONFIG.PATIENTS_PER_ORG * 0.8, max: CONFIG.PATIENTS_PER_ORG * 1.2 });
    const orgFacilities = facilities.filter(f => f.organization_id === org.id);

    // Skip organizations with no facilities
    if (orgFacilities.length === 0) {
      console.log(`  ⚠️ Skipping ${org.name} - no facilities found for patients`);
      continue;
    }

    for (let i = 0; i < patientCount; i++) {
      const birthDate = faker.date.birthdate({ min: 0, max: 90, mode: 'age' });
      const age = calculateAge(birthDate);
      const primaryFacility = randomElement(orgFacilities);

      // Generate patient data that matches the actual database schema
      const firstName = faker.person.firstName();
      const lastName = faker.person.lastName();
      const email = faker.internet.email();
      const phone = faker.phone.number();

      // Emergency contact as text (matches DB schema)
      const emergencyContact = `${faker.person.fullName()} (${randomElement(['spouse', 'parent', 'child', 'sibling', 'friend'])}) - ${faker.phone.number()}`;

      // Address as text (matches DB schema)
      const address = `${faker.location.streetAddress()}, ${faker.location.city()}, ${faker.location.state()} ${faker.location.zipCode()}`;

      // Insurance info as JSONB (matches DB schema)
      const insuranceInfo = generatePatientInsurance(insuranceProviders);

      // Medical history as JSONB (matches DB schema)
      const medicalHistory = generateMedicalHistory(age);

      patients.push({
        organization_id: org.id,
        first_name: firstName,
        last_name: lastName,
        date_of_birth: birthDate.toISOString().split('T')[0],
        gender: randomElement(['male', 'female', 'other', 'prefer_not_to_say']),
        email: email,
        phone: phone,
        address: address,
        emergency_contact: emergencyContact,
        insurance_info: insuranceInfo,
        medical_history: medicalHistory
      });
    }
  }

  if (patients.length === 0) {
    console.log('⚠️ No patients created - no valid organization/facility combinations found');
    return [];
  }

  const { data, error } = await supabase.from('patients').insert(patients).select();
  if (error) throw error;

  logProgress(`Created ${patients.length} patients`);
  return data;
}



async function seedAllergies(patients, healthcareProviders) {
  updateProgress('Allergies');

  const allergies = [];

  for (const patient of patients) {
    // Generate allergies for some patients (40% probability)
    const hasAllergies = faker.datatype.boolean({ probability: 0.4 });
    if (!hasAllergies) continue;

    const allergyCount = faker.number.int({ min: 1, max: 3 });
    const patientAllergies = faker.helpers.arrayElements(COMMON_ALLERGIES, allergyCount);

    for (const allergyData of patientAllergies) {
      allergies.push({
        patient_id: patient.id,
        allergen: allergyData.allergen,
        reaction: randomElement(allergyData.reactions),
        severity: randomElement(['mild', 'moderate', 'severe', 'life_threatening']),
        onset_date: faker.date.past({ years: 10 }).toISOString().split('T')[0],
        status: randomElement(['active', 'active', 'active', 'inactive']),
        reported_by: null // Set to null since we don't have auth users for providers yet
      });
    }
  }

  if (allergies.length === 0) {
    console.log('⚠️ No allergies created');
    return [];
  }

  const { data, error } = await supabase.from('allergies').insert(allergies).select();
  if (error) throw error;

  logProgress(`Created ${allergies.length} allergies`);
  return data;
}

// Helper functions
function generateProviderSpecialty(departmentType) {
  const specialtyMap = {
    'primary_care': 'Family Medicine',
    'pediatrics': 'Pediatrics',
    'cardiology': 'Cardiology',
    'neurology': 'Neurology',
    'orthopedics': 'Orthopedics',
    'emergency': 'Emergency Medicine',
    'laboratory': 'Pathology',
    'pharmacy': 'Clinical Pharmacy',
    'radiology': 'Radiology'
  };

  return specialtyMap[departmentType] || randomElement(MEDICAL_SPECIALTIES);
}

function generateProviderRole(specialty) {
  const clinicalSpecialties = ['Family Medicine', 'Pediatrics', 'Cardiology', 'Neurology', 'Orthopedics', 'Emergency Medicine'];

  if (clinicalSpecialties.includes(specialty)) {
    return randomElement(['physician', 'physician', 'nurse_practitioner', 'registered_nurse', 'medical_assistant']);
  }

  if (specialty === 'Clinical Pharmacy') return 'pharmacist';
  if (specialty === 'Pathology') return 'lab_technician';

  return randomElement(['registered_nurse', 'medical_assistant', 'front_desk']);
}

function generateProviderTitle(role, specialty) {
  const titleMap = {
    'physician': `${specialty} Physician`,
    'nurse_practitioner': `${specialty} Nurse Practitioner`,
    'registered_nurse': 'Registered Nurse',
    'medical_assistant': 'Medical Assistant',
    'pharmacist': 'Clinical Pharmacist',
    'lab_technician': 'Laboratory Technician',
    'front_desk': 'Patient Services Representative'
  };

  return titleMap[role] || 'Healthcare Provider';
}

function generateEmployeeId(orgName) {
  const prefix = orgName.substring(0, 3).toUpperCase();
  const number = faker.string.numeric(6);
  return `${prefix}${number}`;
}

function generateLicenseNumber(role) {
  if (role === 'physician') return `MD${faker.string.numeric(8)}`;
  if (role === 'nurse_practitioner') return `NP${faker.string.numeric(8)}`;
  if (role === 'registered_nurse') return `RN${faker.string.numeric(8)}`;
  if (role === 'pharmacist') return `PH${faker.string.numeric(8)}`;
  return null;
}

function generateCredentials(role, specialty) {
  const baseCredentials = {
    'physician': ['MD', 'Board Certified'],
    'nurse_practitioner': ['MSN', 'FNP-BC'],
    'registered_nurse': ['BSN', 'RN'],
    'pharmacist': ['PharmD', 'RPh'],
    'medical_assistant': ['CMA']
  };

  return baseCredentials[role] || [];
}

function generateMedicalSchool(role) {
  if (role !== 'physician') return null;

  const schools = [
    'Harvard Medical School', 'Johns Hopkins School of Medicine', 'Stanford University School of Medicine',
    'University of Pennsylvania Perelman School of Medicine', 'Columbia University Vagelos College of Physicians and Surgeons',
    'Duke University School of Medicine', 'University of California San Francisco School of Medicine'
  ];

  return randomElement(schools);
}

function generateResidency(specialty) {
  return `${specialty} Residency - ${faker.company.name()} Medical Center`;
}

function generateLanguages() {
  const languages = ['English', 'Spanish', 'Chinese', 'French', 'German', 'Hindi', 'Arabic'];
  const count = faker.number.int({ min: 1, max: 3 });
  return faker.helpers.arrayElements(languages, count);
}

function generateProviderAvailability() {
  return {
    monday: { start: '08:00', end: '17:00', available: true },
    tuesday: { start: '08:00', end: '17:00', available: true },
    wednesday: { start: '08:00', end: '17:00', available: true },
    thursday: { start: '08:00', end: '17:00', available: true },
    friday: { start: '08:00', end: '17:00', available: true },
    saturday: { start: '09:00', end: '12:00', available: faker.datatype.boolean() },
    sunday: { available: false }
  };
}

function generateMRN() {
  return faker.string.numeric(8);
}

function generateMaritalStatus(age) {
  if (age < 18) return 'single';
  if (age < 25) return randomElement(['single', 'single', 'married']);
  return randomElement(['single', 'married', 'divorced', 'widowed']);
}

function generatePatientInsurance(insuranceProviders) {
  const primaryInsurance = randomElement(insuranceProviders);
  const hasSecondary = faker.datatype.boolean({ probability: 0.3 });

  const insurance = {
    primary: {
      provider_id: primaryInsurance.id,
      member_id: faker.string.numeric(12),
      group_number: faker.string.alphanumeric(8),
      plan_name: `${primaryInsurance.name} ${randomElement(['Standard', 'Premium', 'Basic'])}`,
      effective_date: faker.date.past({ years: 2 }).toISOString().split('T')[0],
      copay: faker.number.int({ min: 10, max: 50 }),
      deductible: faker.number.int({ min: 500, max: 5000 })
    }
  };

  if (hasSecondary) {
    const secondaryInsurance = randomElement(insuranceProviders.filter(p => p.id !== primaryInsurance.id));
    insurance.secondary = {
      provider_id: secondaryInsurance.id,
      member_id: faker.string.numeric(12),
      group_number: faker.string.alphanumeric(8),
      plan_name: `${secondaryInsurance.name} ${randomElement(['Standard', 'Premium', 'Basic'])}`,
      effective_date: faker.date.past({ years: 2 }).toISOString().split('T')[0]
    };
  }

  return insurance;
}

function generateMedicalHistory(age) {
  const conditions = [
    'Hypertension', 'Diabetes Type 2', 'High Cholesterol', 'Asthma',
    'Depression', 'Anxiety', 'Arthritis', 'GERD', 'Hypothyroidism'
  ];

  if (age < 18) {
    return {
      chronic_conditions: [],
      past_surgeries: [],
      family_history: randomElement([[], ['Diabetes'], ['Heart Disease'], ['Cancer']])
    };
  }

  const conditionCount = age > 65 ? faker.number.int({ min: 1, max: 4 }) : faker.number.int({ min: 0, max: 2 });

  return {
    chronic_conditions: faker.helpers.arrayElements(conditions, conditionCount),
    past_surgeries: faker.datatype.boolean({ probability: 0.3 }) ? [faker.helpers.arrayElement(['Appendectomy', 'Gallbladder Surgery', 'Hernia Repair'])] : [],
    family_history: faker.helpers.arrayElements(['Diabetes', 'Heart Disease', 'Cancer', 'Stroke'], faker.number.int({ min: 0, max: 3 }))
  };
}



function generateUsername(firstName, lastName) {
  const cleanFirst = firstName.toLowerCase().replace(/[^a-z]/g, '');
  const cleanLast = lastName.toLowerCase().replace(/[^a-z]/g, '');
  const number = faker.number.int({ min: 10, max: 999 });

  return `${cleanFirst}.${cleanLast}${number}`;
}