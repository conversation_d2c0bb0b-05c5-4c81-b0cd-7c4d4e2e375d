import { COMMON_MEDICATIONS, CONFIG } from '#constants';
import { supabase } from '#database';
import { calculateAge, logProgress, randomElement, updateProgress } from '#helpers';
import { faker } from '@faker-js/faker';

// Tier 5: Clinical Operations
export async function generateTier5Clinical() {
  console.log('🏥 Generating Tier 5: Clinical Operations...');
  
  try {
    // Fetch required data from previous tiers
    const { data: organizations, error: orgsError } = await supabase
      .from('organizations')
      .select('*');
    
    if (orgsError) throw orgsError;
    if (!organizations || organizations.length === 0) {
      throw new Error('No organizations found. Please run Tier 2 first.');
    }
    
    const { data: patients, error: patientsError } = await supabase
      .from('patients')
      .select('*');
    
    if (patientsError) throw patientsError;
    if (!patients || patients.length === 0) {
      throw new Error('No patients found. Please run Tier 4 first.');
    }
    
    const { data: healthcareProviders, error: providersError } = await supabase
      .from('healthcare_providers')
      .select('*');
    
    if (providersError) throw providersError;
    if (!healthcareProviders || healthcareProviders.length === 0) {
      throw new Error('No healthcare providers found. Please run Tier 4 first.');
    }
    
    const { data: departments, error: deptsError } = await supabase
      .from('departments')
      .select('*');
    
    if (deptsError) throw deptsError;
    if (!departments || departments.length === 0) {
      throw new Error('No departments found. Please run Tier 3 first.');
    }
    
    const results = {};
    
    // Appointments
    results.appointments = await seedAppointments(organizations, patients, healthcareProviders, departments);
    
    // Medical Records
    results.medicalRecords = await seedMedicalRecords(organizations, patients, healthcareProviders, results.appointments);
    
    // Prescriptions
    results.prescriptions = await seedPrescriptions(results.medicalRecords, healthcareProviders, patients);
    
    // Lab Results
    results.labResults = await seedLabResults(results.medicalRecords, healthcareProviders, patients);
    
    // Vital Signs
    results.vitalSigns = await seedVitalSigns(results.appointments, patients);
    
    console.log('✅ Tier 5 completed successfully');
    return results;
  } catch (error) {
    console.error('❌ Error in Tier 5:', error);
    throw error;
  }
}

async function seedAppointments(organizations, patients, healthcareProviders, departments) {
  updateProgress('Appointments');
  
  const appointments = [];
  
  for (const org of organizations) {
    const orgPatients = patients.filter(p => p.organization_id === org.id);
    const orgProviders = healthcareProviders.filter(p => p.organization_id === org.id);
    const appointmentCount = faker.number.int({ min: CONFIG.APPOINTMENTS_PER_ORG * 0.8, max: CONFIG.APPOINTMENTS_PER_ORG * 1.2 });
    
    for (let i = 0; i < appointmentCount; i++) {
      const patient = randomElement(orgPatients);
      const provider = randomElement(orgProviders);
      const department = departments.find(d => d.id === provider.department_id);
      const appointmentType = generateAppointmentType(provider.specialty);
      const appointmentDate = generateAppointmentDate();
      const duration = generateAppointmentDuration(appointmentType);
      
      appointments.push({
        organization_id: org.id,
        patient_id: patient.id,
        provider_id: provider.id,
        department_id: department.id,
        appointment_date: appointmentDate.date,
        appointment_time: appointmentDate.time,
        duration_minutes: duration,
        type: appointmentType,
        status: generateAppointmentStatus(appointmentDate.date),
        reason: generateAppointmentReason(appointmentType, provider.specialty),
        notes: generateAppointmentNotes(appointmentType),
        scheduling_info: {
          scheduled_by: randomElement(['patient', 'provider', 'staff']),
          scheduled_date: faker.date.past({ days: 30 }).toISOString(),
          preferred_time: randomElement(['morning', 'afternoon', 'evening', 'no_preference']),
          urgency: randomElement(['routine', 'urgent', 'emergent'])
        },
        insurance_info: {
          authorization_required: faker.datatype.boolean({ probability: 0.3 }),
          authorization_number: faker.datatype.boolean({ probability: 0.3 }) ? faker.string.alphanumeric(10) : null,
          copay_amount: patient.insurance.primary.copay
        }
      });
    }
  }

  const { data, error } = await supabase.from('appointments').insert(appointments).select();
  if (error) throw error;
  
  logProgress(`Created ${appointments.length} appointments`);
  return data;
}

async function seedMedicalRecords(organizations, patients, healthcareProviders, appointments) {
  updateProgress('Medical Records');
  
  const medicalRecords = [];
  
  // Create records for completed appointments
  const completedAppointments = appointments.filter(a => a.status === 'completed');
  
  for (const appointment of completedAppointments) {
    const patient = patients.find(p => p.id === appointment.patient_id);
    const provider = healthcareProviders.find(p => p.id === appointment.provider_id);
    const age = calculateAge(patient.date_of_birth);
    
    medicalRecords.push({
      organization_id: appointment.organization_id,
      patient_id: appointment.patient_id,
      provider_id: appointment.provider_id,
      appointment_id: appointment.id,
      encounter_date: appointment.appointment_date,
      encounter_type: appointment.type,
      chief_complaint: generateChiefComplaint(appointment.reason),
      history_present_illness: generateHistoryPresentIllness(appointment.reason),
      physical_exam: generatePhysicalExam(provider.specialty, age),
      assessment: generateAssessment(appointment.reason, provider.specialty),
      plan: generateTreatmentPlan(appointment.reason, provider.specialty),
      diagnoses: generateDiagnoses(appointment.reason, provider.specialty),
      procedures: generateProcedures(appointment.type, provider.specialty),
      follow_up: generateFollowUp(appointment.reason),
      clinical_notes: generateClinicalNotes(appointment.type, provider.specialty),
      vital_signs_recorded: faker.datatype.boolean({ probability: 0.9 }),
      medications_reviewed: faker.datatype.boolean({ probability: 0.8 })
    });
  }

  const { data, error } = await supabase.from('medical_records').insert(medicalRecords).select();
  if (error) throw error;
  
  logProgress(`Created ${medicalRecords.length} medical records`);
  return data;
}

async function seedPrescriptions(medicalRecords, healthcareProviders, patients) {
  updateProgress('Prescriptions');
  
  const prescriptions = [];
  
  for (const record of medicalRecords) {
    const provider = healthcareProviders.find(p => p.id === record.provider_id);
    const patient = patients.find(p => p.id === record.patient_id);
    const age = calculateAge(patient.date_of_birth);
    
    // Not every encounter results in a prescription
    if (faker.datatype.boolean({ probability: 0.6 })) {
      const prescriptionCount = faker.number.int({ min: 1, max: 3 });
      
      for (let i = 0; i < prescriptionCount; i++) {
        const medication = randomElement(COMMON_MEDICATIONS);
        const dosage = randomElement(medication.dosages);
        const frequency = randomElement(medication.frequencies);
        
        prescriptions.push({
          patient_id: record.patient_id,
          provider_id: record.provider_id,
          medical_record_id: record.id,
          medication_name: medication.name,
          dosage: dosage,
          frequency: frequency,
          quantity: generateQuantity(medication.name, frequency),
          refills: faker.number.int({ min: 0, max: 5 }),
          prescribed_date: record.encounter_date,
          start_date: record.encounter_date,
          end_date: generateEndDate(record.encounter_date, medication.name),
          instructions: generateInstructions(medication.name, dosage, frequency),
          indication: generateIndication(medication.name),
          status: generatePrescriptionStatus(),
          pharmacy_info: {
            pharmacy_name: faker.company.name() + ' Pharmacy',
            pharmacy_phone: faker.phone.number(),
            ndc_number: faker.string.numeric(11)
          },
          insurance_info: {
            covered: faker.datatype.boolean({ probability: 0.8 }),
            copay: faker.number.int({ min: 5, max: 50 }),
            prior_auth_required: faker.datatype.boolean({ probability: 0.2 })
          }
        });
      }
    }
  }

  const { data, error } = await supabase.from('prescriptions').insert(prescriptions).select();
  if (error) throw error;
  
  logProgress(`Created ${prescriptions.length} prescriptions`);
  return data;
}

async function seedLabResults(medicalRecords, healthcareProviders, patients) {
  updateProgress('Lab Results');
  
  const labResults = [];
  
  for (const record of medicalRecords) {
    const patient = patients.find(p => p.id === record.patient_id);
    const age = calculateAge(patient.date_of_birth);
    
    // Not every encounter requires lab work
    if (faker.datatype.boolean({ probability: 0.4 })) {
      const labPanels = generateLabPanels(record.assessment, age);
      
      for (const panel of labPanels) {
        labResults.push({
          patient_id: record.patient_id,
          provider_id: record.provider_id,
          medical_record_id: record.id,
          test_name: panel.name,
          test_category: panel.category,
          ordered_date: record.encounter_date,
          collected_date: addDays(record.encounter_date, faker.number.int({ min: 0, max: 3 })),
          completed_date: addDays(record.encounter_date, faker.number.int({ min: 1, max: 5 })),
          results: panel.results,
          reference_ranges: panel.referenceRanges,
          abnormal_flags: panel.abnormalFlags,
          status: 'completed',
          lab_facility: faker.company.name() + ' Laboratory',
          technician: faker.person.fullName(),
          interpretation: generateLabInterpretation(panel.abnormalFlags)
        });
      }
    }
  }

  const { data, error } = await supabase.from('lab_results').insert(labResults).select();
  if (error) throw error;
  
  logProgress(`Created ${labResults.length} lab results`);
  return data;
}

async function seedVitalSigns(appointments, patients) {
  updateProgress('Vital Signs');
  
  const vitalSigns = [];
  
  // Create vital signs for most appointments
  for (const appointment of appointments) {
    if (faker.datatype.boolean({ probability: 0.9 })) {
      const patient = patients.find(p => p.id === appointment.patient_id);
      const age = calculateAge(patient.date_of_birth);
      
      vitalSigns.push({
        patient_id: appointment.patient_id,
        appointment_id: appointment.id,
        measured_date: appointment.appointment_date,
        measured_time: appointment.appointment_time,
        height: generateHeight(age),
        weight: generateWeight(age),
        bmi: null, // Will be calculated
        temperature: generateTemperature(),
        blood_pressure: generateBloodPressure(age),
        heart_rate: generateHeartRate(age),
        respiratory_rate: generateRespiratoryRate(age),
        oxygen_saturation: generateOxygenSaturation(),
        pain_scale: generatePainScale(),
        measured_by: faker.person.fullName(),
        notes: generateVitalSignsNotes()
      });
    }
  }

  // Calculate BMI
  vitalSigns.forEach(vs => {
    if (vs.height && vs.weight) {
      const heightM = vs.height * 0.0254; // Convert inches to meters
      const weightKg = vs.weight * 0.453592; // Convert pounds to kg
      vs.bmi = parseFloat((weightKg / (heightM * heightM)).toFixed(1));
    }
  });

  const { data, error } = await supabase.from('vital_signs').insert(vitalSigns).select();
  if (error) throw error;
  
  logProgress(`Created ${vitalSigns.length} vital signs records`);
  return data;
}

// Helper functions
function generateAppointmentType(specialty) {
  const types = {
    'Family Medicine': ['Annual Physical', 'Follow-up', 'Sick Visit', 'Preventive Care'],
    'Pediatrics': ['Well Child', 'Sick Visit', 'Vaccination', 'Development Check'],
    'Cardiology': ['Consultation', 'Echocardiogram', 'Stress Test', 'Follow-up'],
    'Emergency Medicine': ['Emergency Visit', 'Urgent Care', 'Trauma Evaluation'],
    'default': ['Consultation', 'Follow-up', 'New Patient', 'Routine Visit']
  };
  
  const typeList = types[specialty] || types.default;
  return randomElement(typeList);
}

function generateAppointmentDate() {
  // Mix of past, present, and future appointments
  const timeframe = randomElement(['past', 'present', 'future']);
  let date;
  
  if (timeframe === 'past') {
    date = faker.date.past({ years: 1 });
  } else if (timeframe === 'future') {
    date = faker.date.future({ years: 1 });
  } else {
    date = faker.date.recent({ days: 7 });
  }
  
  // Set to business hours
  const hour = faker.number.int({ min: 8, max: 17 });
  const minute = randomElement([0, 15, 30, 45]);
  date.setHours(hour, minute, 0, 0);
  
  return {
    date: date.toISOString().split('T')[0],
    time: date.toTimeString().split(' ')[0].substring(0, 5)
  };
}

function generateAppointmentDuration(type) {
  const durations = {
    'Annual Physical': 45,
    'Follow-up': 15,
    'Consultation': 30,
    'Emergency Visit': 60,
    'Vaccination': 15
  };
  
  return durations[type] || 30;
}

function generateAppointmentStatus(appointmentDate) {
  const date = new Date(appointmentDate);
  const now = new Date();
  
  if (date > now) {
    return randomElement(['scheduled', 'confirmed']);
  } else if (date < now) {
    return randomElement(['completed', 'completed', 'completed', 'no_show', 'cancelled']);
  } else {
    return randomElement(['in_progress', 'scheduled']);
  }
}

function generateAppointmentReason(type, specialty) {
  const reasons = {
    'Annual Physical': 'Annual health maintenance examination',
    'Follow-up': 'Follow-up for ongoing medical condition',
    'Sick Visit': 'Acute illness evaluation',
    'Consultation': `${specialty} consultation`,
    'Emergency Visit': 'Emergency medical evaluation'
  };
  
  return reasons[type] || 'Medical evaluation';
}

function generateAppointmentNotes(type) {
  if (faker.datatype.boolean({ probability: 0.3 })) {
    return faker.lorem.sentence();
  }
  return null;
}

function generateChiefComplaint(reason) {
  const complaints = [
    'Chest pain', 'Shortness of breath', 'Abdominal pain', 'Headache', 
    'Fatigue', 'Cough', 'Fever', 'Joint pain', 'Back pain', 'Dizziness'
  ];
  
  if (reason.includes('Annual')) return 'Annual physical examination';
  if (reason.includes('Follow-up')) return 'Follow-up visit';
  
  return randomElement(complaints);
}

function generateHistoryPresentIllness(reason) {
  return faker.lorem.sentences(faker.number.int({ min: 2, max: 4 }));
}

function generatePhysicalExam(specialty, age) {
  const exams = [
    'General appearance: well-appearing, alert and oriented',
    'Vital signs: stable and within normal limits',
    'HEENT: normocephalic, atraumatic',
    'Cardiovascular: regular rate and rhythm, no murmurs',
    'Pulmonary: clear to auscultation bilaterally',
    'Abdomen: soft, non-tender, non-distended'
  ];
  
  return faker.helpers.arrayElements(exams, faker.number.int({ min: 3, max: 6 })).join('. ');
}

function generateAssessment(reason, specialty) {
  const assessments = {
    'Chest pain': 'Chest pain, likely musculoskeletal',
    'Headache': 'Tension headache',
    'Fatigue': 'Fatigue, rule out underlying causes',
    'Annual': 'Annual health maintenance'
  };
  
  for (const key in assessments) {
    if (reason.includes(key)) return assessments[key];
  }
  
  return 'Clinical assessment pending further evaluation';
}

function generateTreatmentPlan(reason, specialty) {
  return faker.lorem.sentences(faker.number.int({ min: 1, max: 3 }));
}

function generateDiagnoses(reason, specialty) {
  const diagnoses = [
    { code: 'Z00.00', description: 'Encounter for general adult medical examination' },
    { code: 'R50.9', description: 'Fever, unspecified' },
    { code: 'M25.50', description: 'Pain in unspecified joint' },
    { code: 'R06.02', description: 'Shortness of breath' }
  ];
  
  return faker.helpers.arrayElements(diagnoses, faker.number.int({ min: 1, max: 2 }));
}

function generateProcedures(type, specialty) {
  if (faker.datatype.boolean({ probability: 0.3 })) {
    const procedures = [
      { code: '99213', description: 'Office visit, established patient' },
      { code: '93000', description: 'Electrocardiogram' },
      { code: '85025', description: 'Complete blood count' }
    ];
    
    return faker.helpers.arrayElements(procedures, 1);
  }
  return [];
}

function generateFollowUp(reason) {
  const followUps = [
    'Return in 2 weeks if symptoms persist',
    'Follow up in 3 months',
    'Return as needed',
    'Schedule annual physical'
  ];
  
  return randomElement(followUps);
}

function generateClinicalNotes(type, specialty) {
  return faker.lorem.sentences(faker.number.int({ min: 2, max: 5 }));
}

function generateQuantity(medicationName, frequency) {
  if (frequency.includes('daily')) return faker.number.int({ min: 30, max: 90 });
  if (frequency.includes('twice')) return faker.number.int({ min: 60, max: 180 });
  return faker.number.int({ min: 14, max: 30 });
}

function generateEndDate(startDate, medicationName) {
  const start = new Date(startDate);
  const days = faker.number.int({ min: 30, max: 90 });
  start.setDate(start.getDate() + days);
  return start.toISOString().split('T')[0];
}

function generateInstructions(medicationName, dosage, frequency) {
  return `Take ${dosage} ${frequency} as prescribed. Take with food if stomach upset occurs.`;
}

function generateIndication(medicationName) {
  const indications = {
    'Lisinopril': 'Hypertension',
    'Metformin': 'Type 2 Diabetes',
    'Atorvastatin': 'Hyperlipidemia',
    'Omeprazole': 'GERD'
  };
  
  return indications[medicationName] || 'As prescribed';
}

function generatePrescriptionStatus() {
  return randomElement(['active', 'active', 'active', 'discontinued', 'completed']);
}

function generateLabPanels(assessment, age) {
  const panels = [];
  
  // Common panels based on assessment
  if (assessment.includes('physical') || assessment.includes('Annual')) {
    panels.push(generateCBCPanel(), generateBMPPanel());
  }
  
  if (panels.length === 0) {
    panels.push(generateCBCPanel());
  }
  
  return panels;
}

function generateCBCPanel() {
  return {
    name: 'Complete Blood Count (CBC)',
    category: 'Hematology',
    results: {
      'WBC': faker.number.float({ min: 4.0, max: 11.0, precision: 0.1 }),
      'RBC': faker.number.float({ min: 4.2, max: 5.4, precision: 0.1 }),
      'Hemoglobin': faker.number.float({ min: 12.0, max: 16.0, precision: 0.1 }),
      'Hematocrit': faker.number.float({ min: 36.0, max: 48.0, precision: 0.1 }),
      'Platelets': faker.number.int({ min: 150, max: 450 })
    },
    referenceRanges: {
      'WBC': '4.0-11.0 K/uL',
      'RBC': '4.2-5.4 M/uL',
      'Hemoglobin': '12.0-16.0 g/dL',
      'Hematocrit': '36.0-48.0%',
      'Platelets': '150-450 K/uL'
    },
    abnormalFlags: generateAbnormalFlags(['WBC', 'RBC', 'Hemoglobin', 'Hematocrit', 'Platelets'])
  };
}

function generateBMPPanel() {
  return {
    name: 'Basic Metabolic Panel (BMP)',
    category: 'Chemistry',
    results: {
      'Glucose': faker.number.int({ min: 70, max: 140 }),
      'BUN': faker.number.int({ min: 7, max: 20 }),
      'Creatinine': faker.number.float({ min: 0.6, max: 1.2, precision: 0.1 }),
      'Sodium': faker.number.int({ min: 135, max: 145 }),
      'Potassium': faker.number.float({ min: 3.5, max: 5.0, precision: 0.1 }),
      'Chloride': faker.number.int({ min: 98, max: 107 })
    },
    referenceRanges: {
      'Glucose': '70-100 mg/dL',
      'BUN': '7-20 mg/dL',
      'Creatinine': '0.6-1.2 mg/dL',
      'Sodium': '135-145 mEq/L',
      'Potassium': '3.5-5.0 mEq/L',
      'Chloride': '98-107 mEq/L'
    },
    abnormalFlags: generateAbnormalFlags(['Glucose', 'BUN', 'Creatinine', 'Sodium', 'Potassium', 'Chloride'])
  };
}

function generateAbnormalFlags(testNames) {
  const flags = {};
  for (const test of testNames) {
    if (faker.datatype.boolean({ probability: 0.2 })) {
      flags[test] = randomElement(['HIGH', 'LOW']);
    }
  }
  return flags;
}

function generateLabInterpretation(abnormalFlags) {
  if (Object.keys(abnormalFlags).length === 0) {
    return 'Results within normal limits';
  }
  return `Abnormal results noted: ${Object.keys(abnormalFlags).join(', ')}. Recommend clinical correlation.`;
}

function addDays(dateString, days) {
  const date = new Date(dateString);
  date.setDate(date.getDate() + days);
  return date.toISOString().split('T')[0];
}

function generateHeight(age) {
  if (age < 18) {
    return faker.number.int({ min: 36, max: 70 }); // inches
  }
  return faker.number.int({ min: 60, max: 76 });
}

function generateWeight(age) {
  if (age < 18) {
    return faker.number.int({ min: 50, max: 160 }); // pounds
  }
  return faker.number.int({ min: 110, max: 250 });
}

function generateTemperature() {
  return faker.number.float({ min: 97.0, max: 99.5, precision: 0.1 });
}

function generateBloodPressure(age) {
  const systolic = age > 65 ? faker.number.int({ min: 110, max: 160 }) : faker.number.int({ min: 100, max: 140 });
  const diastolic = faker.number.int({ min: 60, max: 90 });
  return `${systolic}/${diastolic}`;
}

function generateHeartRate(age) {
  if (age < 18) return faker.number.int({ min: 80, max: 120 });
  return faker.number.int({ min: 60, max: 100 });
}

function generateRespiratoryRate(age) {
  if (age < 18) return faker.number.int({ min: 16, max: 24 });
  return faker.number.int({ min: 12, max: 20 });
}

function generateOxygenSaturation() {
  return faker.number.int({ min: 95, max: 100 });
}

function generatePainScale() {
  return faker.datatype.boolean({ probability: 0.7 }) ? faker.number.int({ min: 0, max: 10 }) : null;
}

function generateVitalSignsNotes() {
  if (faker.datatype.boolean({ probability: 0.2 })) {
    return faker.lorem.sentence();
  }
  return null;
} 