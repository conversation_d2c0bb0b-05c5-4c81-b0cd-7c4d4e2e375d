{"name": "spritely", "version": "0.1.0", "private": true, "scripts": {"build": "turbo run build", "dev": "npm run generate-types && turbo run dev:no-types", "dev:setup": "./scripts/dev-setup.sh", "dev:full": "npm run supabase:setup && npm run generate-types && turbo run dev:no-types", "dev:development": "npm run env:development && turbo run dev", "dev:staging": "npm run env:staging && turbo run dev", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types", "generate-types": "cd packages/supabase-types && npx supabase gen types typescript --local --schema public > src/generated-types.ts && npm run build", "generate-types:development": "cd packages/supabase-types && npx supabase gen types typescript --project-id clwxzkbihuzjdhkjjubx --schema public > src/generated-types.ts && npm run build", "generate-types:staging": "cd packages/supabase-types && npx supabase gen types typescript --project-id vxzubpcxlkdwqphshimb --schema public > src/generated-types.ts && npm run build", "generate-types:master": "cd packages/supabase-types && npx supabase gen types typescript --project-id kkwjqitvnomdlgxtjvzy --schema public > src/generated-types.ts && npm run build", "generate-types:ci": "cd packages/supabase-types && source ../../.env.ci && npx supabase gen types typescript --project-id $SUPABASE_PROJECT_ID --schema public > src/generated-types.ts && npm run build", "supabase:start": "npm run --workspace=@spritely/supabase supabase:start", "supabase:stop": "npm run --workspace=@spritely/supabase supabase:stop", "supabase:setup": "npm run --workspace=@spritely/supabase supabase:setup", "env:local": "./scripts/env-setup.sh local", "env:development": "./scripts/env-setup.sh development", "env:staging": "./scripts/env-setup.sh staging", "env:ci:development": "./scripts/env-setup.sh development --ci", "env:ci:staging": "./scripts/env-setup.sh staging --ci", "supabase:push": "supabase db push", "supabase:link:development": "supabase link --project-ref clwxzkbihuzjdhkjjubx", "supabase:link:staging": "supabase link --project-ref vxzubpcxlkdwqphshimb", "supabase:link:master": "supabase link --project-ref kkwjqitvnomdlgxtjvzy", "1password:setup": "./scripts/setup-1password-vault.sh", "1password:organize": "./scripts/organize-1password.sh", "build:packages": "turbo run build --filter=@spritely/*", "seed": "node scripts/seed.js", "seed:all": "node scripts/seed.js", "db:reset": "supabase db reset --local", "seed:tier1": "node scripts/tiers/tier1-foundation.js", "seed:tier2": "node scripts/tiers/tier2-organizations.js", "seed:tier3": "node scripts/tiers/tier3-departments.js", "seed:tier4": "node scripts/tiers/tier4-users.js", "seed:tier5": "node scripts/tiers/tier5-clinical.js", "seed:tier6": "node scripts/tiers/tier6-administrative.js", "seed:tier7": "node scripts/tiers/tier7-communication.js", "seed:tier8": "node scripts/tiers/tier8-advanced-clinical.js", "seed:tier9": "node scripts/tiers/tier9-patient-engagement.js", "seed:tier10": "node scripts/tiers/tier10-operations.js", "seed:tier11": "node scripts/tiers/tier11-workflows.js", "seed:tier12": "node scripts/tiers/tier12-analytics-audit.js", "seed:tier13": "node scripts/tiers/tier13-communication-enhancement.js"}, "devDependencies": {"@faker-js/faker": "^9.8.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "prettier": "^3.5.3", "supabase": "^2.20.5", "tailwindcss": "^4.1.3", "ts-node": "^10.9.2", "turbo": "^2.5.3", "typescript": "^5.0.4"}, "engines": {"node": ">=18"}, "packageManager": "npm@10.9.2", "workspaces": ["apps/*", "packages/*"], "dependencies": {"embla-carousel-autoplay": "^8.6.0", "node-fetch": "^2.7.0"}}