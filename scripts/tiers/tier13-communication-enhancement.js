import { supabase } from '#database';
import { getRandomElement, getRandomElements, logProgress, updateProgress } from '#helpers';
import { faker } from '@faker-js/faker';

export async function generateTier13CommunicationEnhancement() {
  console.log('💬 Generating Tier 13: Communication Enhancement...');
  
  try {
    const { data: organizations } = await supabase.from('organizations').select('id');
    const { data: providers } = await supabase.from('healthcare_providers').select('id, organization_id');
    const { data: patients } = await supabase.from('patients').select('id, organization_id');
    const { data: conversations } = await supabase.from('conversations').select('id, organization_id');
    const { data: messages } = await supabase.from('messages').select('id, conversation_id');

    // Generate Notification Preferences
    console.log('Creating notification preferences...');
    const notificationPreferences = [];
    const notificationTypes = [
      'appointment_reminder', 'lab_result', 'prescription_update', 
      'medical_record_update', 'task_assignment', 'message', 'alert'
    ];
    
    const allUsers = [...providers, ...patients];
    
    for (const user of allUsers) {
      for (const notificationType of notificationTypes) {
        notificationPreferences.push({
          user_id: user.id,
          type: notificationType,
          email_enabled: faker.datatype.boolean({ probability: 0.8 }),
          sms_enabled: faker.datatype.boolean({ probability: 0.6 }),
          push_enabled: faker.datatype.boolean({ probability: 0.7 }),
          in_app_enabled: faker.datatype.boolean({ probability: 0.9 }),
          metadata: {
            frequency: getRandomElement(['immediate', 'hourly', 'daily', 'weekly']),
            quiet_hours: {
              enabled: faker.datatype.boolean({ probability: 0.6 }),
              start_time: '22:00',
              end_time: '08:00'
            },
            delivery_preferences: {
              urgent_only: faker.datatype.boolean({ probability: 0.2 }),
              digest_mode: faker.datatype.boolean({ probability: 0.3 }),
              custom_sound: faker.datatype.boolean({ probability: 0.1 })
            }
          }
        });
      }
    }
    
    if (notificationPreferences.length > 0) {
      const { error } = await supabase.from('notification_preferences').insert(notificationPreferences);
      if (error) throw error;
      logProgress(`✅ Created ${notificationPreferences.length} notification preferences`);
    }

    // Generate Notification Templates
    console.log('Creating notification templates...');
    const notificationTemplates = [];
    
    for (const organization of organizations) {
      for (const notificationType of notificationTypes) {
        notificationTemplates.push({
          organization_id: organization.id,
          name: `${notificationType.replace('_', ' ').toUpperCase()} Template`,
          type: notificationType,
          subject_template: generateSubjectTemplate(notificationType),
          content_template: generateContentTemplate(notificationType),
          metadata_template: {
            priority: getRandomElement(['low', 'medium', 'high', 'urgent']),
            category: getRandomElement(['clinical', 'administrative', 'system']),
            tags: getRandomElements(['patient_care', 'scheduling', 'medication', 'results'], 2),
            auto_send: faker.datatype.boolean({ probability: 0.7 }),
            approval_required: faker.datatype.boolean({ probability: 0.3 }),
            languages_supported: ['en', 'es'],
            accessibility: {
              screen_reader_optimized: faker.datatype.boolean({ probability: 0.8 }),
              high_contrast_available: faker.datatype.boolean({ probability: 0.6 })
            }
          }
        });
      }
    }
    
    if (notificationTemplates.length > 0) {
      const { error } = await supabase.from('notification_templates').insert(notificationTemplates);
      if (error) throw error;
      logProgress(`✅ Created ${notificationTemplates.length} notification templates`);
    }

    // Generate Message States
    console.log('Creating message states...');
    const messageStates = [];
    
    for (const message of messages.slice(0, Math.floor(messages.length * 0.8))) {
      const conversation = conversations.find(c => c.id === message.conversation_id);
      if (!conversation) continue;
      
      const conversationUsers = [
        ...providers.filter(p => p.organization_id === conversation.organization_id),
        ...patients.filter(p => p.organization_id === conversation.organization_id)
      ].slice(0, 5); // Limit to 5 users per conversation
      
      for (const user of conversationUsers) {
        const states = ['sent', 'delivered', 'read'];
        const currentState = getRandomElement(states);
        
        messageStates.push({
          message_id: message.id,
          user_id: user.id,
          state: currentState,
          updated_at: faker.date.recent({ days: 7 }).toISOString(),
          metadata: {
            delivery_attempt: faker.number.int({ min: 1, max: 3 }),
            device_type: getRandomElement(['mobile', 'desktop', 'tablet']),
            read_timestamp: currentState === 'read' ? 
              faker.date.recent({ days: 1 }).toISOString() : null,
            delivery_method: getRandomElement(['push', 'email', 'sms', 'in_app']),
            failure_reason: faker.datatype.boolean({ probability: 0.05 }) ? 
              getRandomElement(['network_error', 'invalid_address', 'user_blocked']) : null
          }
        });
      }
    }
    
    if (messageStates.length > 0) {
      const { error } = await supabase.from('message_states').insert(messageStates);
      if (error) throw error;
      logProgress(`✅ Created ${messageStates.length} message states`);
    }

    // Generate Conversation Participants
    console.log('Creating conversation participants...');
    const conversationParticipants = [];
    
    for (const conversation of conversations) {
      const availableUsers = [
        ...providers.filter(p => p.organization_id === conversation.organization_id),
        ...patients.filter(p => p.organization_id === conversation.organization_id)
      ];
      
      const numParticipants = faker.number.int({ min: 2, max: 6 });
      const selectedUsers = getRandomElements(availableUsers, Math.min(numParticipants, availableUsers.length));
      
      selectedUsers.forEach((user, index) => {
        const isProvider = providers.some(p => p.id === user.id);
        
        conversationParticipants.push({
          conversation_id: conversation.id,
          user_id: user.id,
          role: index === 0 ? 'owner' : 
                isProvider ? getRandomElement(['moderator', 'participant']) : 'participant',
          last_read_at: faker.datatype.boolean({ probability: 0.7 }) ? 
            faker.date.recent({ days: 3 }).toISOString() : null,
          metadata: {
            joined_at: faker.date.past({ years: 1 }).toISOString(),
            notification_settings: {
              muted: faker.datatype.boolean({ probability: 0.1 }),
              mentions_only: faker.datatype.boolean({ probability: 0.2 }),
              custom_ringtone: faker.datatype.boolean({ probability: 0.1 })
            },
            participation_stats: {
              messages_sent: faker.number.int({ min: 0, max: 50 }),
              files_shared: faker.number.int({ min: 0, max: 10 }),
              reactions_given: faker.number.int({ min: 0, max: 25 }),
              last_active: faker.date.recent({ days: 7 }).toISOString()
            },
            permissions: {
              can_add_participants: isProvider && faker.datatype.boolean({ probability: 0.8 }),
              can_edit_conversation: isProvider && faker.datatype.boolean({ probability: 0.6 }),
              can_delete_messages: isProvider && faker.datatype.boolean({ probability: 0.4 }),
              can_share_files: faker.datatype.boolean({ probability: 0.9 })
            }
          }
        });
      });
    }
    
    if (conversationParticipants.length > 0) {
      const { error } = await supabase.from('conversation_participants').insert(conversationParticipants);
      if (error) throw error;
      logProgress(`✅ Created ${conversationParticipants.length} conversation participants`);
    }

    updateProgress('tier13', 'completed');
    console.log('✅ Tier 13: Communication Enhancement completed successfully!');
    
  } catch (error) {
    console.error('❌ Error in Tier 13:', error);
    throw error;
  }
}

function generateSubjectTemplate(notificationType) {
  const templates = {
    appointment_reminder: "Appointment Reminder: {{appointment_date}} with {{provider_name}}",
    lab_result: "Lab Results Available: {{test_name}}",
    prescription_update: "Prescription Update: {{medication_name}}",
    medical_record_update: "Medical Record Updated by {{provider_name}}",
    task_assignment: "New Task Assigned: {{task_title}}",
    message: "New Message from {{sender_name}}",
    alert: "Important Alert: {{alert_type}}"
  };
  
  return templates[notificationType] || `{{organization_name}} Notification`;
}

function generateContentTemplate(notificationType) {
  const templates = {
    appointment_reminder: `
Dear {{patient_name}},

This is a reminder that you have an appointment scheduled for:

Date: {{appointment_date}}
Time: {{appointment_time}}
Provider: {{provider_name}}
Location: {{facility_name}}

Please arrive 15 minutes early for check-in.

If you need to reschedule, please call {{facility_phone}}.

Best regards,
{{organization_name}}
    `,
    lab_result: `
Dear {{patient_name}},

Your lab results for {{test_name}} are now available.

{{#if critical_result}}
IMPORTANT: Please contact your provider immediately regarding these results.
{{/if}}

You can view your results in the patient portal or contact your provider at {{provider_phone}}.

{{organization_name}}
    `,
    prescription_update: `
Dear {{patient_name}},

Your prescription for {{medication_name}} has been {{action}}.

{{#if new_prescription}}
Please pick up your medication at {{pharmacy_name}}.
{{/if}}

If you have any questions, please contact your provider.

{{organization_name}}
    `
  };
  
  return templates[notificationType] || `
Dear {{recipient_name}},

{{notification_content}}

Thank you,
{{organization_name}}
  `;
} 