import { supabase } from '#database';
import { getRandomElement, getRandomElements, logProgress, updateProgress } from '#helpers';
import { faker } from '@faker-js/faker';

export async function generateTier10Operations() {
  console.log('⚙️ Generating Tier 10: Operations...');
  
  try {
    // Get existing data for relationships
    const { data: organizations } = await supabase.from('organizations').select('id');
    const { data: facilities } = await supabase.from('facilities').select('id, organization_id');
    const { data: departments } = await supabase.from('departments').select('id, facility_id');
    const { data: providers } = await supabase.from('healthcare_providers').select('id, organization_id');

    // Generate Inventory Items
    console.log('Creating inventory items...');
    const inventoryItems = [];
    const medicalSupplies = [
      { name: 'Surgical Gloves', type: 'PPE', unit: 'boxes' },
      { name: 'Face Masks', type: 'PPE', unit: 'boxes' },
      { name: 'Syringes', type: 'medical_device', unit: 'units' },
      { name: 'Bandages', type: 'consumable', unit: 'rolls' },
      { name: 'Gauze Pads', type: 'consumable', unit: 'packs' },
      { name: 'Thermometers', type: 'medical_device', unit: 'units' },
      { name: 'Blood Pressure Cuffs', type: 'medical_device', unit: 'units' },
      { name: 'Stethoscopes', type: 'medical_device', unit: 'units' },
      { name: 'Alcohol Swabs', type: 'consumable', unit: 'boxes' },
      { name: 'IV Bags', type: 'medication', unit: 'units' },
      { name: 'Catheter Kits', type: 'medical_device', unit: 'kits' },
      { name: 'Suture Materials', type: 'surgical', unit: 'packages' },
      { name: 'Oxygen Tanks', type: 'equipment', unit: 'tanks' },
      { name: 'Wheelchairs', type: 'equipment', unit: 'units' },
      { name: 'Hospital Beds', type: 'equipment', unit: 'units' }
    ];
    
    for (const facility of facilities) {
      for (const supply of medicalSupplies) {
        inventoryItems.push({
          facility_id: facility.id,
          organization_id: facility.organization_id,
          name: supply.name,
          type: supply.type,
          unit: supply.unit,
          quantity: faker.number.int({ min: 5, max: 1000 }),
          minimum_quantity: faker.number.int({ min: 10, max: 50 }),
          location: getRandomElement([
            'Supply Room A', 'Central Storage', 'Pharmacy', 'Emergency Department',
            'ICU Storage', 'Operating Room', 'Main Warehouse', 'Department Storage'
          ]),
          metadata: {
            manufacturer: faker.company.name(),
            lot_number: faker.string.alphanumeric({ length: 8, casing: 'upper' }),
            expiration_date: faker.date.future({ years: 2 }).toISOString().split('T')[0],
            cost_per_unit: faker.number.float({ min: 0.50, max: 500, multipleOf: 0.01 }),
            supplier: faker.company.name(),
            last_restocked: faker.date.recent({ days: 30 }).toISOString().split('T')[0],
            reorder_point: faker.number.int({ min: 15, max: 75 }),
            storage_requirements: getRandomElement(['room_temperature', 'refrigerated', 'frozen', 'controlled_substance'])
          }
        });
      }
    }
    
    if (inventoryItems.length > 0) {
      const { error } = await supabase.from('inventory_items').insert(inventoryItems);
      if (error) throw error;
      logProgress(`✅ Created ${inventoryItems.length} inventory items`);
    }

    // Get created inventory items for transactions
    const { data: createdItems } = await supabase.from('inventory_items').select('id, name, quantity');

    // Generate Inventory Transactions
    console.log('Creating inventory transactions...');
    const inventoryTransactions = [];
    
    for (const item of createdItems.slice(0, Math.floor(createdItems.length * 0.5))) {
      const numTransactions = faker.number.int({ min: 1, max: 5 });
      for (let i = 0; i < numTransactions; i++) {
        const transactionType = getRandomElement(['inbound', 'outbound', 'adjustment', 'transfer']);
        let quantity = 0;
        
        switch (transactionType) {
          case 'inbound':
            quantity = faker.number.int({ min: 10, max: 100 });
            break;
          case 'outbound':
            quantity = -faker.number.int({ min: 1, max: Math.min(20, item.quantity) });
            break;
          case 'adjustment':
            quantity = faker.number.int({ min: -10, max: 10 });
            break;
          case 'transfer':
            quantity = -faker.number.int({ min: 1, max: Math.min(15, item.quantity) });
            break;
        }
        
        inventoryTransactions.push({
          item_id: item.id,
          transaction_type: transactionType,
          quantity: quantity,
          reason: getRandomElement([
            'Regular restocking',
            'Patient care usage',
            'Emergency department consumption',
            'Expired items removal',
            'Transfer to other department',
            'Inventory audit adjustment',
            'Damage/loss',
            'Equipment maintenance'
          ]),
          performed_by: getRandomElement(providers)?.id,
          metadata: {
            cost_impact: Math.abs(quantity) * faker.number.float({ min: 1, max: 50, multipleOf: 0.01 }),
            reference_number: faker.string.alphanumeric({ length: 10, casing: 'upper' }),
            approval_required: faker.datatype.boolean({ probability: 0.3 }),
            approved_by: faker.datatype.boolean({ probability: 0.9 }) ? getRandomElement(providers)?.id : null,
            documentation_url: faker.internet.url()
          }
        });
      }
    }
    
    if (inventoryTransactions.length > 0) {
      const { error } = await supabase.from('inventory_transactions').insert(inventoryTransactions);
      if (error) throw error;
      logProgress(`✅ Created ${inventoryTransactions.length} inventory transactions`);
    }

    // Generate Tasks
    console.log('Creating tasks...');
    const tasks = [];
    const taskTypes = [
      'patient_care', 'administrative', 'maintenance', 'quality_assurance',
      'training', 'compliance', 'emergency_response', 'documentation'
    ];
    
    for (const organization of organizations) {
      const orgProviders = providers.filter(p => p.organization_id === organization.id);
      const orgDepartments = departments.filter(d => 
        facilities.find(f => f.id === d.facility_id)?.organization_id === organization.id
      );
      
      const numTasks = faker.number.int({ min: 20, max: 50 });
      for (let i = 0; i < numTasks; i++) {
        const assignedTo = getRandomElement(orgProviders);
        const assignedBy = getRandomElement(orgProviders.filter(p => p.id !== assignedTo?.id));
        
        tasks.push({
          organization_id: organization.id,
          department_id: getRandomElement(orgDepartments)?.id,
          title: getRandomElement([
            'Review patient documentation',
            'Update medical records',
            'Prepare for quality audit',
            'Complete mandatory training',
            'Inventory count verification',
            'Equipment maintenance check',
            'Patient safety assessment',
            'Insurance verification',
            'Schedule follow-up appointments',
            'Update emergency protocols'
          ]),
          description: faker.lorem.paragraph(),
          priority: getRandomElement(['low', 'medium', 'high', 'urgent']),
          status: getRandomElement(['pending', 'in_progress', 'completed', 'cancelled', 'blocked']),
          assigned_to: assignedTo?.id,
          assigned_by: assignedBy?.id,
          due_date: faker.date.future({ days: 30 }).toISOString().split('T')[0],
          completed_at: faker.datatype.boolean({ probability: 0.4 }) ? 
            faker.date.recent({ days: 14 }).toISOString() : null,
          related_to: {
            type: getRandomElement(['patient', 'provider', 'department', 'facility', 'organization']),
            id: faker.string.uuid(),
            description: faker.lorem.sentence()
          },
          metadata: {
            estimated_hours: faker.number.float({ min: 0.5, max: 8, multipleOf: 0.25 }),
            skill_requirements: getRandomElements([
              'clinical_knowledge', 'administrative_skills', 'technical_expertise',
              'communication', 'problem_solving', 'attention_to_detail'
            ], faker.number.int({ min: 1, max: 3 })),
            urgency_level: faker.number.int({ min: 1, max: 5 }),
            resource_requirements: getRandomElements([
              'computer_access', 'medical_equipment', 'patient_charts',
              'supervisor_approval', 'collaboration'
            ], faker.number.int({ min: 0, max: 3 }))
          }
        });
      }
    }
    
    if (tasks.length > 0) {
      const { error } = await supabase.from('tasks').insert(tasks);
      if (error) throw error;
      logProgress(`✅ Created ${tasks.length} tasks`);
    }

    // Get created tasks for comments and watchers
    const { data: createdTasks } = await supabase.from('tasks').select('id, organization_id');

    // Generate Task Comments
    console.log('Creating task comments...');
    const taskComments = [];
    
    for (const task of createdTasks.slice(0, Math.floor(createdTasks.length * 0.6))) {
      const numComments = faker.number.int({ min: 1, max: 5 });
      const taskProviders = providers.filter(p => p.organization_id === task.organization_id);
      
      for (let i = 0; i < numComments; i++) {
        taskComments.push({
          task_id: task.id,
          user_id: getRandomElement(taskProviders)?.id,
          content: getRandomElement([
            'Task has been started, will update progress soon.',
            'Encountered some issues, may need additional resources.',
            'Completed first phase, moving to next step.',
            'Need clarification on requirements.',
            'Task is on track for completion by due date.',
            'Requires approval from supervisor before proceeding.',
            'Additional training may be needed for this task.',
            'Task completed successfully, documenting results.'
          ]),
          attachments: faker.datatype.boolean({ probability: 0.2 }) ? [
            {
              filename: faker.system.fileName(),
              url: faker.internet.url(),
              size: faker.number.int({ min: 1024, max: 5242880 }),
              type: getRandomElement(['pdf', 'doc', 'jpg', 'png'])
            }
          ] : null
        });
      }
    }
    
    if (taskComments.length > 0) {
      const { error } = await supabase.from('task_comments').insert(taskComments);
      if (error) throw error;
      logProgress(`✅ Created ${taskComments.length} task comments`);
    }

    // Generate Task Watchers
    console.log('Creating task watchers...');
    const taskWatchers = [];
    
    for (const task of createdTasks.slice(0, Math.floor(createdTasks.length * 0.4))) {
      const taskProviders = providers.filter(p => p.organization_id === task.organization_id);
      const numWatchers = faker.number.int({ min: 1, max: 4 });
      const selectedWatchers = getRandomElements(taskProviders, Math.min(numWatchers, taskProviders.length));
      
      for (const watcher of selectedWatchers) {
        taskWatchers.push({
          task_id: task.id,
          user_id: watcher.id
        });
      }
    }
    
    if (taskWatchers.length > 0) {
      const { error } = await supabase.from('task_watchers').insert(taskWatchers);
      if (error) throw error;
      logProgress(`✅ Created ${taskWatchers.length} task watchers`);
    }

    // Generate Templates
    console.log('Creating templates...');
    const templates = [];
    const templateTypes = [
      'medical_note', 'discharge_summary', 'consent_form', 'referral_letter',
      'patient_instruction', 'lab_report', 'prescription', 'care_plan',
      'assessment_form', 'progress_note'
    ];
    
    for (const organization of organizations) {
      for (const templateType of templateTypes) {
        templates.push({
          organization_id: organization.id,
          name: `${templateType.replace('_', ' ').toUpperCase()} Template`,
          type: templateType,
          content: generateTemplateContent(templateType),
          metadata: {
            version: '1.0',
            created_by: getRandomElement(providers.filter(p => p.organization_id === organization.id))?.id,
            usage_count: faker.number.int({ min: 0, max: 100 }),
            last_used: faker.date.recent({ days: 30 }).toISOString(),
            approval_required: faker.datatype.boolean({ probability: 0.3 }),
            compliance_verified: faker.datatype.boolean({ probability: 0.8 }),
            applicable_departments: getRandomElements([
              'primary_care', 'emergency', 'cardiology', 'neurology'
            ], faker.number.int({ min: 1, max: 3 }))
          }
        });
      }
    }
    
    if (templates.length > 0) {
      const { error } = await supabase.from('templates').insert(templates);
      if (error) throw error;
      logProgress(`✅ Created ${templates.length} templates`);
    }

    updateProgress('tier10', 'completed');
    console.log('✅ Tier 10: Operations completed successfully!');
    
  } catch (error) {
    console.error('❌ Error in Tier 10:', error);
    throw error;
  }
}

function generateTemplateContent(templateType) {
  const templates = {
    medical_note: `
**PATIENT:** {{patient_name}}
**DATE:** {{date}}
**PROVIDER:** {{provider_name}}

**CHIEF COMPLAINT:** {{chief_complaint}}

**HISTORY OF PRESENT ILLNESS:**
{{history}}

**PHYSICAL EXAMINATION:**
{{physical_exam}}

**ASSESSMENT:**
{{assessment}}

**PLAN:**
{{plan}}

**PROVIDER SIGNATURE:** {{provider_signature}}
    `,
    discharge_summary: `
**DISCHARGE SUMMARY**

**Patient:** {{patient_name}}
**DOB:** {{date_of_birth}}
**Admission Date:** {{admission_date}}
**Discharge Date:** {{discharge_date}}

**DIAGNOSIS:** {{diagnosis}}

**HOSPITAL COURSE:** {{hospital_course}}

**DISCHARGE MEDICATIONS:** {{medications}}

**FOLLOW-UP:** {{follow_up}}

**DISCHARGE INSTRUCTIONS:** {{instructions}}
    `,
    consent_form: `
**INFORMED CONSENT FOR {{procedure_name}}**

I acknowledge that Dr. {{provider_name}} has explained the procedure of {{procedure_name}} to me.

**RISKS AND BENEFITS:**
{{risks_benefits}}

**ALTERNATIVES:**
{{alternatives}}

**PATIENT CONSENT:**
I consent to the above procedure.

Patient Signature: _________________ Date: _______
    `
  };
  
  return templates[templateType] || `Template content for ${templateType}:\n\n{{content}}`;
} 