import { faker } from '@faker-js/faker';

// Utility functions
export const randomElement = (array) => faker.helpers.arrayElement(array);
export const randomElements = (array, count) => faker.helpers.arrayElements(array, count);

// Aliases for consistency with existing imports
export const getRandomElement = randomElement;
export const getRandomElements = randomElements;

// Date utilities
export const generateDateInRange = (startDays, endDays) => {
  const start = new Date();
  start.setDate(start.getDate() - startDays);
  const end = new Date();
  end.setDate(end.getDate() - endDays);
  return faker.date.between({ from: start, to: end }).toISOString().split('T')[0];
};

export function generatePastDateTime(daysAgo) {
  const date = new Date();
  date.setDate(date.getDate() - faker.number.int({ min: 1, max: daysAgo }));
  return date.toISOString();
}

export function generateFutureDate(daysInFuture) {
  const date = new Date();
  date.setDate(date.getDate() + faker.number.int({ min: 1, max: daysInFuture }));
  return date.toISOString();
}

export function calculateAge(birthDate) {
  const today = new Date();
  const birth = new Date(birthDate);
  let age = today.getFullYear() - birth.getFullYear();
  const monthDiff = today.getMonth() - birth.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--;
  }
  
  return age;
}

// Medical utilities
export function generateMedicalRecordNumber() {
  return faker.string.numeric(8);
}

export function generatePatientId() {
  return `PAT${faker.string.numeric(6)}`;
}

export function generateProviderId() {
  return `PRV${faker.string.numeric(6)}`;
}

// Insurance utilities
export function generateInsuranceMemberId() {
  return faker.string.alphanumeric(12);
}

export function generateGroupNumber() {
  return faker.string.alphanumeric(8);
}

// Re-export progress functions for convenience
export { getProgress, logProgress, updateProgress } from './progress.js';
