import { CONFIG } from '#constants';
import { supabase } from '#database';
import { logProgress, randomElement, updateProgress } from '#helpers';
import { faker } from '@faker-js/faker';

// Tier 2: Organizations & Core
export async function generateTier2Organizations() {
  console.log('🏢 Generating Tier 2: Organizations...');
  
  try {
    const results = {};
    
    // Organizations
    results.organizations = await seedOrganizations();
    
    // Facilities  
    results.facilities = await seedFacilities(results.organizations);
    
    updateProgress('tier2', 'completed');
    console.log('✅ Tier 2: Organizations completed successfully!');
    
    return results;
  } catch (error) {
    console.error('❌ Error in Tier 2:', error);
    throw error;
  }
}

async function seedOrganizations() {
  updateProgress('Organizations');
  
  const organizationTypes = [
    'Hospital System', 'Medical Center', 'Community Hospital', 
    'Specialty Clinic', 'Urgent Care Network', 'Primary Care Group'
  ];
  
  const organizations = Array.from({ length: CONFIG.ORGANIZATIONS }, () => ({
    name: generateOrganizationName(),
    type: randomElement(organizationTypes),
    settings: {
      timezone: randomElement(['EST', 'CST', 'MST', 'PST']),
      businessHours: {
        monday: '08:00-17:00',
        tuesday: '08:00-17:00', 
        wednesday: '08:00-17:00',
        thursday: '08:00-17:00',
        friday: '08:00-17:00',
        saturday: '09:00-12:00',
        sunday: 'closed'
      },
      emergencyContact: faker.phone.number(),
      contact_info: {
        phone: faker.phone.number(),
        email: faker.internet.email(),
        website: faker.internet.url(),
        address: {
          street: faker.location.streetAddress(),
          city: faker.location.city(),
          state: faker.location.state(),
          zipCode: faker.location.zipCode(),
          country: 'USA'
        }
      },
      metadata: {
        founded: faker.date.past({ years: 50 }).getFullYear(),
        accreditation: randomElement(['Joint Commission', 'AAAHC', 'NCQA', 'DNV']),
        bedCount: faker.number.int({ min: 50, max: 500 }),
        employeeCount: faker.number.int({ min: 100, max: 2000 })
      }
    },
    subscription_tier: randomElement(['basic', 'professional', 'enterprise']),
    billing_info: {
      billingContact: faker.person.fullName(),
      billingEmail: faker.internet.email(),
      paymentMethod: randomElement(['invoice', 'credit_card', 'ach']),
      taxId: faker.string.alphanumeric(9)
    }
  }));

  const { data, error } = await supabase.from('organizations').insert(organizations).select();
  if (error) throw error;
  
  logProgress(`Created ${organizations.length} organizations`);
  return data;
}

async function seedFacilities(organizations) {
  updateProgress('Facilities');
  
  const facilityTypes = [
    'Main Hospital', 'Outpatient Clinic', 'Emergency Department', 
    'Surgical Center', 'Imaging Center', 'Laboratory', 'Pharmacy'
  ];
  
  const facilities = [];
  
  for (const org of organizations) {
    const facilityCount = faker.number.int({ min: 2, max: 3 });
    
    for (let i = 0; i < facilityCount; i++) {
      facilities.push({
        organization_id: org.id,
        name: generateFacilityName(org.name, i),
        type: i === 0 ? 'Main Hospital' : randomElement(facilityTypes.slice(1)),
        address: {
          street: faker.location.streetAddress(),
          city: faker.location.city(),
          state: faker.location.state(),
          zipCode: faker.location.zipCode(),
          country: 'USA'
        },
        contact_info: {
          phone: faker.phone.number(),
          email: faker.internet.email(),
          fax: faker.phone.number()
        },
        operating_hours: {
          weekdays: '06:00-22:00',
          weekends: '08:00-18:00',
          emergency: '24/7'
        },
        settings: {
          capacity: {
            beds: faker.number.int({ min: 20, max: 200 }),
            rooms: faker.number.int({ min: 10, max: 100 }),
            parkingSpaces: faker.number.int({ min: 50, max: 300 })
          },
          services: generateFacilityServices(),
          accessibility: {
            wheelchairAccessible: true,
            parkingAvailable: true,
            publicTransport: faker.datatype.boolean()
          }
        }
      });
    }
  }

  const { data, error } = await supabase.from('facilities').insert(facilities).select();
  if (error) throw error;
  
  logProgress(`Created ${facilities.length} facilities`);
  return data;
}

// Helper functions
function generateOrganizationName() {
  const prefixes = ['St.', 'Mount', 'General', 'University', 'Regional', 'Community', 'Memorial'];
  const middles = ['Mary', 'Joseph', 'Sinai', 'Hope', 'Mercy', 'Grace', 'Cedar', 'Oak'];
  const suffixes = ['Medical Center', 'Hospital', 'Health System', 'Healthcare', 'Clinic Network'];
  
  return `${randomElement(prefixes)} ${randomElement(middles)} ${randomElement(suffixes)}`;
}

function generateFacilityName(orgName, index) {
  if (index === 0) {
    return `${orgName} - Main Campus`;
  }
  
  const locations = ['North', 'South', 'East', 'West', 'Downtown', 'Uptown', 'Riverside', 'Hillside'];
  const types = ['Outpatient Center', 'Specialty Clinic', 'Urgent Care', 'Surgery Center'];
  
  return `${orgName} - ${randomElement(locations)} ${randomElement(types)}`;
}

function generateFacilityServices() {
  const allServices = [
    'Emergency Medicine', 'Internal Medicine', 'Pediatrics', 'Cardiology',
    'Orthopedics', 'Neurology', 'Oncology', 'Radiology', 'Laboratory',
    'Pharmacy', 'Physical Therapy', 'Surgery', 'ICU', 'Maternity'
  ];
  
  const serviceCount = faker.number.int({ min: 5, max: 12 });
  return faker.helpers.arrayElements(allServices, serviceCount);
} 