#!/usr/bin/env node

import { supabase } from './config/database.js';

async function cleanDatabase() {
  console.log('🧹 Starting Database Cleanup...');

  try {
    // List of all tables to truncate (in reverse dependency order)
    const tables = [
      // Tier 13: Communication Enhancement
      'notification_preferences',
      'message_states',
      'conversation_participants',

      // Tier 12: Analytics & Audit
      'analytics_events',
      'analytics_metrics',
      'audit_logs',
      'activity_logs',

      // Tier 11: Workflows
      'workflow_instances',
      'workflow_logs',
      'organization_invites',

      // Tier 10: Operations
      'inventory_items',
      'task_watchers',
      'task_comments',
      'tasks',
      'templates',

      // Tier 9: Patient Engagement
      'patient_alerts',
      'patient_questionnaires',
      'patient_education_records',
      'patient_portal_settings',

      // Tier 8: Advanced Clinical
      'allergies',
      'immunizations',
      'orders',
      'referrals',
      'care_team_members',

      // Tier 7: Communication
      'clinical_notes',
      'messages',
      'conversations',
      'documents',

      // Tier 6: Administrative
      'billing_records',
      'claims',
      'payments',
      'invoices',

      // Tier 5: Clinical
      'appointments',
      'medical_records',
      'prescriptions',
      'lab_results',
      'vital_signs',

      // Tier 4: Users
      'patients',
      'healthcare_providers',

      // Tier 3: Departments
      'teams',
      'departments',

      // Tier 2: Organizations
      'facilities',
      'organizations',

      // Tier 1: Foundation
      'insurance_providers',
      'education_materials',
      'role_permissions',
      'user_roles',
      'billing_codes'
    ];

    console.log(`Cleaning ${tables.length} tables...`);

    for (const table of tables) {
      try {
        const { error } = await supabase
          .from(table)
          .delete()
          .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all except impossible ID

        if (error && !error.message.includes('does not exist')) {
          console.log(`⚠️  Warning cleaning ${table}: ${error.message}`);
        } else {
          console.log(`✅ Cleaned ${table}`);
        }
      } catch (err) {
        console.log(`⚠️  Warning with ${table}: ${err.message}`);
      }
    }

    console.log('🎉 Database cleanup completed successfully!');

  } catch (error) {
    console.error('❌ Error during database cleanup:', error);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  cleanDatabase();
}

export { cleanDatabase };
