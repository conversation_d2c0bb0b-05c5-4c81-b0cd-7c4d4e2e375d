import { faker } from '@faker-js/faker';

// Configuration
export const CONFIG = {
  ORGANIZATIONS: 18,
  FACILITIES_PER_ORG: faker.helpers.arrayElement([2, 3]),
  DEPARTMENTS_PER_FACILITY: 10,
  PROVIDERS_PER_ORG: faker.number.int({ min: 50, max: 100 }),
  PATIENTS_PER_ORG: faker.number.int({ min: 500, max: 1000 }),
  APPOINTMENTS_PER_ORG: faker.number.int({ min: 200, max: 500 }),
  RECORDS_PER_ORG: faker.number.int({ min: 100, max: 300 }),
  MEDICATIONS_PER_ORG: faker.number.int({ min: 50, max: 150 }),
  LAB_RESULTS_PER_ORG: faker.number.int({ min: 20, max: 50 }),
};

// Medical data constants
export const MEDICAL_SPECIALTIES = [
  'Cardiology', 'Pediatrics', 'Neurology', 'Orthopedics', 'Dermatology',
  'Gastroenterology', 'Oncology', 'Psychiatry', 'Endocrinology', 'Radiology',
  'Anesthesiology', 'Emergency Medicine', 'Family Medicine', 'Internal Medicine',
  'Obstetrics and Gynecology', 'Ophthalmology', 'Otolaryngology', 'Pathology',
  'Physical Medicine', 'Plastic Surgery', 'Pulmonology', 'Rheumatology',
  'Urology', 'Infectious Disease', 'Nephrology', 'Hematology'
];

export const COMMON_MEDICATIONS = [
  {
    name: 'Lisinopril',
    dosages: ['5mg', '10mg', '20mg', '40mg'],
    frequencies: ['once daily', 'twice daily']
  },
  {
    name: 'Metformin',
    dosages: ['500mg', '850mg', '1000mg'],
    frequencies: ['once daily', 'twice daily']
  },
  {
    name: 'Atorvastatin',
    dosages: ['10mg', '20mg', '40mg', '80mg'],
    frequencies: ['once daily at bedtime']
  },
  {
    name: 'Omeprazole',
    dosages: ['20mg', '40mg'],
    frequencies: ['once daily before breakfast']
  },
  {
    name: 'Amlodipine',
    dosages: ['2.5mg', '5mg', '10mg'],
    frequencies: ['once daily']
  },
  {
    name: 'Simvastatin',
    dosages: ['10mg', '20mg', '40mg', '80mg'],
    frequencies: ['once daily at bedtime']
  },
  {
    name: 'Losartan',
    dosages: ['25mg', '50mg', '100mg'],
    frequencies: ['once daily', 'twice daily']
  },
  {
    name: 'Hydrochlorothiazide',
    dosages: ['12.5mg', '25mg', '50mg'],
    frequencies: ['once daily']
  },
  {
    name: 'Sertraline',
    dosages: ['25mg', '50mg', '100mg'],
    frequencies: ['once daily']
  },
  {
    name: 'Escitalopram',
    dosages: ['5mg', '10mg', '20mg'],
    frequencies: ['once daily']
  },
  {
    name: 'Levothyroxine',
    dosages: ['25mcg', '50mcg', '75mcg', '100mcg', '125mcg'],
    frequencies: ['once daily on empty stomach']
  },
  {
    name: 'Gabapentin',
    dosages: ['100mg', '300mg', '400mg', '600mg', '800mg'],
    frequencies: ['three times daily', 'twice daily']
  },
  {
    name: 'Tramadol',
    dosages: ['50mg', '100mg'],
    frequencies: ['every 4-6 hours as needed', 'twice daily']
  },
  {
    name: 'Ibuprofen',
    dosages: ['200mg', '400mg', '600mg', '800mg'],
    frequencies: ['every 6-8 hours as needed', 'three times daily']
  },
  {
    name: 'Acetaminophen',
    dosages: ['325mg', '500mg', '650mg'],
    frequencies: ['every 4-6 hours as needed', 'four times daily']
  }
];

export const COMMON_ALLERGIES = [
  {
    allergen: 'Penicillin',
    reactions: ['rash', 'hives', 'swelling', 'difficulty breathing']
  },
  {
    allergen: 'Peanuts',
    reactions: ['hives', 'swelling', 'difficulty breathing', 'anaphylaxis']
  },
  {
    allergen: 'Shellfish',
    reactions: ['hives', 'swelling', 'nausea', 'vomiting']
  },
  {
    allergen: 'Latex',
    reactions: ['rash', 'itching', 'swelling']
  },
  {
    allergen: 'Sulfa drugs',
    reactions: ['rash', 'fever', 'joint pain']
  },
  {
    allergen: 'Iodine',
    reactions: ['rash', 'hives', 'difficulty breathing']
  },
  {
    allergen: 'Aspirin',
    reactions: ['rash', 'difficulty breathing', 'stomach upset']
  },
  {
    allergen: 'Codeine',
    reactions: ['nausea', 'vomiting', 'rash']
  }
];

export const DEPARTMENT_TYPES = [
  'primary_care', 'pediatrics', 'cardiology', 'neurology', 'orthopedics',
  'emergency', 'laboratory', 'pharmacy', 'radiology', 'billing', 'administration'
];

export const BILLING_CODES = {
  CPT: [
    { code: '99201', description: 'Office visit, new patient, level 1' },
    { code: '99202', description: 'Office visit, new patient, level 2' },
    { code: '99203', description: 'Office visit, new patient, level 3' },
    { code: '99204', description: 'Office visit, new patient, level 4' },
    { code: '99205', description: 'Office visit, new patient, level 5' },
    { code: '99211', description: 'Office visit, established patient, level 1' },
    { code: '99212', description: 'Office visit, established patient, level 2' },
    { code: '99213', description: 'Office visit, established patient, level 3' },
    { code: '99214', description: 'Office visit, established patient, level 4' },
    { code: '99215', description: 'Office visit, established patient, level 5' },
    { code: '99396', description: 'Preventive medicine evaluation, 40-64 years' },
    { code: '99397', description: 'Preventive medicine evaluation, 65+ years' },
    { code: '93000', description: 'Electrocardiogram, routine ECG' },
    { code: '85025', description: 'Complete blood count (CBC)' },
    { code: '80053', description: 'Comprehensive metabolic panel' },
    { code: '36415', description: 'Collection of venous blood by venipuncture' }
  ],
  ICD10: [
    { code: 'Z00.00', description: 'Encounter for general adult medical examination without abnormal findings' },
    { code: 'I10', description: 'Essential hypertension' },
    { code: 'E11.9', description: 'Type 2 diabetes mellitus without complications' },
    { code: 'E78.5', description: 'Hyperlipidemia, unspecified' },
    { code: 'M25.50', description: 'Pain in unspecified joint' },
    { code: 'R50.9', description: 'Fever, unspecified' },
    { code: 'R06.02', description: 'Shortness of breath' },
    { code: 'R51', description: 'Headache' },
    { code: 'K21.9', description: 'Gastro-esophageal reflux disease without esophagitis' },
    { code: 'F32.9', description: 'Major depressive disorder, single episode, unspecified' }
  ]
}; 