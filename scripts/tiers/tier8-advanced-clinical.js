import { COMMON_ALLERGIES, COMMON_MEDICATIONS } from '#constants';
import { supabase } from '#database';
import { getRandomElement, getRandomElements, logProgress, updateProgress } from '#helpers';
import { faker } from '@faker-js/faker';

export async function generateTier8AdvancedClinical() {
  console.log('🧬 Generating Tier 8: Advanced Clinical Data...');
  
  try {
    // Get existing data for relationships
    const { data: patients } = await supabase.from('patients').select('id, organization_id');
    const { data: providers } = await supabase.from('healthcare_providers').select('id, organization_id, specialization');
    const { data: medicalRecords } = await supabase.from('medical_records').select('id, patient_id, provider_id, visit_date');
    
    // Generate Allergies
    console.log('Creating allergies...');
    const allergies = [];
    for (const patient of patients.slice(0, Math.floor(patients.length * 0.7))) {
      const numAllergies = faker.number.int({ min: 0, max: 4 });
      for (let i = 0; i < numAllergies; i++) {
        const allergy = getRandomElement(COMMON_ALLERGIES);
        allergies.push({
          patient_id: patient.id,
          allergen: allergy.name,
          severity: getRandomElement(['mild', 'moderate', 'severe', 'life_threatening']),
          reaction: allergy.reaction,
          onset_date: faker.date.past({ years: 10 }).toISOString().split('T')[0],
          status: getRandomElement(['active', 'inactive', 'resolved']),
          reported_by: getRandomElement(providers.filter(p => p.organization_id === patient.organization_id))?.id,
          metadata: {
            verified: faker.datatype.boolean(),
            last_reaction_date: faker.date.recent({ days: 365 }).toISOString().split('T')[0]
          }
        });
      }
    }
    
    if (allergies.length > 0) {
      const { error } = await supabase.from('allergies').insert(allergies);
      if (error) throw error;
      logProgress(`✅ Created ${allergies.length} allergies`);
    }

    // Generate Immunizations
    console.log('Creating immunizations...');
    const immunizations = [];
    const vaccines = [
      'COVID-19', 'Influenza', 'Hepatitis B', 'Tetanus', 'MMR', 'Pneumococcal', 
      'HPV', 'Meningococcal', 'Varicella', 'Zoster'
    ];
    
    for (const patient of patients) {
      const numVaccines = faker.number.int({ min: 2, max: 8 });
      const selectedVaccines = getRandomElements(vaccines, numVaccines);
      
      for (const vaccine of selectedVaccines) {
        immunizations.push({
          patient_id: patient.id,
          vaccine_name: vaccine,
          vaccine_code: faker.string.alphanumeric({ length: 6, casing: 'upper' }),
          administered_date: faker.date.past({ years: 5 }).toISOString().split('T')[0],
          administered_by: getRandomElement(providers.filter(p => p.organization_id === patient.organization_id))?.id,
          dose_number: faker.number.int({ min: 1, max: 3 }),
          manufacturer: getRandomElement(['Pfizer', 'Moderna', 'Johnson & Johnson', 'GSK', 'Merck']),
          lot_number: faker.string.alphanumeric({ length: 8, casing: 'upper' }),
          expiration_date: faker.date.future({ years: 2 }).toISOString().split('T')[0],
          route: getRandomElement(['intramuscular', 'subcutaneous', 'oral', 'nasal']),
          site: getRandomElement(['left_arm', 'right_arm', 'left_thigh', 'right_thigh']),
          notes: faker.lorem.sentence(),
          metadata: {
            reaction: faker.datatype.boolean({ probability: 0.1 }) ? 'mild soreness' : null,
            next_due: faker.date.future({ years: 1 }).toISOString().split('T')[0]
          }
        });
      }
    }
    
    if (immunizations.length > 0) {
      const { error } = await supabase.from('immunizations').insert(immunizations);
      if (error) throw error;
      logProgress(`✅ Created ${immunizations.length} immunizations`);
    }

    // Generate Orders
    console.log('Creating orders...');
    const orders = [];
    const orderTypes = ['lab', 'imaging', 'medication', 'procedure', 'referral', 'consultation'];
    
    for (const record of medicalRecords) {
      if (faker.datatype.boolean({ probability: 0.6 })) {
        const orderType = getRandomElement(orderTypes);
        let orderDetails = {};
        
        switch (orderType) {
          case 'lab':
            orderDetails = {
              tests: getRandomElements(['CBC', 'BMP', 'Lipid Panel', 'TSH', 'HbA1c'], faker.number.int({ min: 1, max: 3 })),
              fasting_required: faker.datatype.boolean(),
              collection_method: getRandomElement(['venipuncture', 'fingerstick', 'urine'])
            };
            break;
          case 'imaging':
            orderDetails = {
              study_type: getRandomElement(['X-Ray', 'CT', 'MRI', 'Ultrasound', 'Mammogram']),
              body_part: getRandomElement(['chest', 'abdomen', 'head', 'spine', 'extremity']),
              contrast: faker.datatype.boolean()
            };
            break;
          case 'medication':
            const med = getRandomElement(COMMON_MEDICATIONS);
            orderDetails = {
              medication: med.name,
              dosage: med.dosage,
              frequency: med.frequency,
              duration: faker.number.int({ min: 7, max: 90 }) + ' days'
            };
            break;
          case 'procedure':
            orderDetails = {
              procedure_name: getRandomElement(['Colonoscopy', 'EKG', 'Stress Test', 'Biopsy', 'Endoscopy']),
              anesthesia_required: faker.datatype.boolean(),
              prep_instructions: faker.lorem.paragraph()
            };
            break;
        }
        
        orders.push({
          patient_id: record.patient_id,
          ordering_provider_id: record.provider_id,
          order_type: orderType,
          order_details: orderDetails,
          ordered_at: record.visit_date,
          scheduled_date: faker.date.future({ days: 30 }).toISOString().split('T')[0],
          priority: getRandomElement(['routine', 'urgent', 'stat', 'emergency']),
          status: getRandomElement(['pending', 'approved', 'in_progress', 'completed', 'cancelled']),
          notes: faker.lorem.sentence(),
          diagnosis_codes: [faker.string.numeric({ length: 5 })],
          completed_at: faker.datatype.boolean({ probability: 0.7 }) ? 
            faker.date.recent({ days: 14 }).toISOString() : null,
          metadata: {
            insurance_approved: faker.datatype.boolean({ probability: 0.9 }),
            estimated_cost: faker.number.float({ min: 50, max: 2000, multipleOf: 0.01 })
          }
        });
      }
    }
    
    if (orders.length > 0) {
      const { error } = await supabase.from('orders').insert(orders);
      if (error) throw error;
      logProgress(`✅ Created ${orders.length} orders`);
    }

    // Generate Referrals
    console.log('Creating referrals...');
    const referrals = [];
    
    for (const record of medicalRecords.slice(0, Math.floor(medicalRecords.length * 0.2))) {
      const referringProvider = providers.find(p => p.id === record.provider_id);
      const specialistProviders = providers.filter(p => 
        p.organization_id === referringProvider?.organization_id && 
        p.specialization && 
        p.id !== record.provider_id
      );
      
      if (specialistProviders.length > 0) {
        referrals.push({
          patient_id: record.patient_id,
          referring_provider_id: record.provider_id,
          referred_to_provider_id: getRandomElement(specialistProviders).id,
          referral_date: record.visit_date,
          reason: getRandomElement([
            'Specialist consultation required',
            'Complex case management',
            'Second opinion needed',
            'Specialized treatment required',
            'Diagnostic workup'
          ]),
          priority: getRandomElement(['routine', 'urgent', 'emergency']),
          status: getRandomElement(['pending', 'scheduled', 'completed', 'cancelled']),
          scheduled_date: faker.date.future({ days: 30 }).toISOString().split('T')[0],
          completed_date: faker.datatype.boolean({ probability: 0.6 }) ? 
            faker.date.recent({ days: 14 }).toISOString().split('T')[0] : null,
          notes: faker.lorem.paragraph(),
          metadata: {
            urgency_level: faker.number.int({ min: 1, max: 5 }),
            follow_up_required: faker.datatype.boolean(),
            insurance_authorization: faker.string.alphanumeric({ length: 10 })
          }
        });
      }
    }
    
    if (referrals.length > 0) {
      const { error } = await supabase.from('referrals').insert(referrals);
      if (error) throw error;
      logProgress(`✅ Created ${referrals.length} referrals`);
    }

    // Generate Care Team Members
    console.log('Creating care team members...');
    const careTeamMembers = [];
    
    for (const patient of patients.slice(0, Math.floor(patients.length * 0.4))) {
      const organizationProviders = providers.filter(p => p.organization_id === patient.organization_id);
      const teamSize = faker.number.int({ min: 2, max: 5 });
      const selectedProviders = getRandomElements(organizationProviders, Math.min(teamSize, organizationProviders.length));
      
      selectedProviders.forEach((provider, index) => {
        const roles = ['Primary Care Provider', 'Specialist', 'Care Coordinator', 'Case Manager', 'Social Worker'];
        careTeamMembers.push({
          patient_id: patient.id,
          provider_id: provider.id,
          role: getRandomElement(roles),
          start_date: faker.date.past({ years: 2 }).toISOString().split('T')[0],
          end_date: faker.datatype.boolean({ probability: 0.1 }) ? 
            faker.date.recent({ days: 30 }).toISOString().split('T')[0] : null,
          primary_contact: index === 0,
          notes: faker.lorem.sentence(),
          metadata: {
            communication_preference: getRandomElement(['phone', 'email', 'secure_message']),
            availability: {
              days: getRandomElements(['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'], 
                faker.number.int({ min: 3, max: 5 })),
              hours: '9:00 AM - 5:00 PM'
            }
          }
        });
      });
    }
    
    if (careTeamMembers.length > 0) {
      const { error } = await supabase.from('care_team_members').insert(careTeamMembers);
      if (error) throw error;
      logProgress(`✅ Created ${careTeamMembers.length} care team members`);
    }

    updateProgress('tier8', 'completed');
    console.log('✅ Tier 8: Advanced Clinical Data completed successfully!');
    
  } catch (error) {
    console.error('❌ Error in Tier 8:', error);
    throw error;
  }
} 