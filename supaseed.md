# SupaSeed: Universal Supabase Seed Generator

## Overview
A TypeScript-first, schema-agnostic seed data generator that reads ANY Supabase generated types file and automatically creates realistic, relationship-aware test data for any domain.

## Supabase Type Generation Analysis

### Universal Structure (ALL Supabase Apps)
Every Supabase `generated-types.ts` file follows this exact pattern:

```typescript
// 1. JSON type definition (always present)
export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[]

// 2. Main Database interface (always present)
export type Database = {
  public: {
    Tables: { /* table definitions */ }
    Views: { /* view definitions */ }
    Functions: { /* function definitions */ }
    Enums: { /* enum definitions */ }
    CompositeTypes: { /* composite type definitions */ }
  }
}

// 3. Helper type exports (always present)
export type Tables<T> = Database['public']['Tables'][T]['Row']
export type TablesInsert<T> = Database['public']['Tables'][T]['Insert']
export type TablesUpdate<T> = Database['public']['Tables'][T]['Update']
export type Enums<T> = Database['public']['Enums'][T]

// 4. Constants object with enum values (always present) - THIS IS KEY!
export const Constants = {
  public: {
    Enums: { /* runtime enum values */ }
  }
} as const
```

### 🚨 Critical Insight: Runtime vs Compile-time
**TypeScript types are erased at runtime** - we cannot introspect `Row`, `Insert`, `Update` types programmatically.

**Solution**: Use the `Constants` object and analyze actual database schema via Supabase client introspection.

### Table Structure Pattern (Universal)
Every table follows this exact structure:
```typescript
table_name: {
  Row: {        // SELECT result type
    id: string  // Primary keys
    field: type // All columns with their types
    created_at: string | null  // Common patterns
    updated_at: string | null
  }
  Insert: {     // INSERT payload type
    id?: string // Optional if auto-generated
    field: type // Required fields
    created_at?: string | null // Optional with defaults
  }
  Update: {     // UPDATE payload type
    id?: string // All fields optional
    field?: type
  }
  Relationships: [  // Foreign key metadata
    {
      foreignKeyName: string
      columns: string[]
      isOneToOne: boolean
      referencedRelation: string
      referencedColumns: string[]
    }
  ]
}
```

### Enum Structure Pattern (Universal)
```typescript
// Type definition
Enums: {
  enum_name: "value1" | "value2" | "value3"
}

// Runtime values
Constants: {
  public: {
    Enums: {
      enum_name: ["value1", "value2", "value3"]
    }
  }
}
```

## Core Architecture

### 1. Universal Type Introspection
The system must work with ANY Supabase schema by analyzing these universal patterns:

```typescript
// Generic constraint that works with ANY Supabase generated types
type SupabaseDatabase = {
  public: {
    Tables: Record<string, {
      Row: Record<string, any>
      Insert: Record<string, any>
      Update: Record<string, any>
      Relationships: Array<{
        foreignKeyName: string
        columns: string[]
        isOneToOne: boolean
        referencedRelation: string
        referencedColumns: string[]
      }>
    }>
    Views?: Record<string, { Row: Record<string, any> }>
    Functions?: Record<string, any>
    Enums?: Record<string, string>
    CompositeTypes?: Record<string, any>
  }
}

// Type utilities to extract information from ANY schema
type ExtractTableNames<T extends SupabaseDatabase> = keyof T['public']['Tables']
type ExtractEnumNames<T extends SupabaseDatabase> = keyof T['public']['Enums']
type ExtractTableInsert<T extends SupabaseDatabase, TableName extends ExtractTableNames<T>> =
  T['public']['Tables'][TableName]['Insert']
```

### 2. Runtime Schema Analysis Engine
Since TypeScript types are erased at runtime, we use **Supabase client introspection**:

```typescript
interface ColumnInfo {
  name: string
  type: string
  nullable: boolean
  hasDefault: boolean
  foreignKey?: {
    table: string
    column: string
  }
  enumValues?: string[]
}

class RuntimeSchemaAnalyzer {
  constructor(
    private supabase: SupabaseClient,
    private constants: { public: { Enums: Record<string, readonly string[]> } }
  ) {}

  // Get actual table schema from database
  async getTableSchema(tableName: string): Promise<ColumnInfo[]> {
    // Use Supabase's built-in schema introspection
    const { data: columns } = await this.supabase
      .from('information_schema.columns')
      .select('*')
      .eq('table_name', tableName)
      .eq('table_schema', 'public')

    return columns.map(col => ({
      name: col.column_name,
      type: this.mapPostgresType(col.data_type, col.udt_name),
      nullable: col.is_nullable === 'YES',
      hasDefault: col.column_default !== null,
      enumValues: this.getEnumValues(col.udt_name)
    }))
  }

  // Get foreign key relationships
  async getForeignKeys(tableName: string): Promise<ForeignKeyInfo[]> {
    const { data } = await this.supabase.rpc('get_foreign_keys', {
      table_name: tableName
    })
    return data || []
  }

  // Build dependency graph using actual FK constraints
  async buildDependencyGraph(): Promise<Map<string, string[]>> {
    const { data: tables } = await this.supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')

    const graph = new Map<string, string[]>()

    for (const table of tables) {
      const fks = await this.getForeignKeys(table.table_name)
      const dependencies = fks.map(fk => fk.referenced_table)
      graph.set(table.table_name, dependencies)
    }

    return graph
  }

  // Map PostgreSQL types to our internal types
  private mapPostgresType(dataType: string, udtName: string): string {
    if (udtName && this.constants.public.Enums[udtName]) {
      return 'enum'
    }

    const typeMap: Record<string, string> = {
      'character varying': 'string',
      'text': 'string',
      'integer': 'number',
      'bigint': 'number',
      'boolean': 'boolean',
      'jsonb': 'json',
      'json': 'json',
      'uuid': 'string',
      'timestamp with time zone': 'string',
      'date': 'string'
    }

    return typeMap[dataType] || 'string'
  }

  // Get enum values from Constants
  private getEnumValues(enumName: string): string[] | undefined {
    return this.constants.public.Enums[enumName] ?
      [...this.constants.public.Enums[enumName]] : undefined
  }
}
```

### 3. Universal Data Generation Engine
The key insight: Generate data based on **column name patterns** that work across ALL domains:

```typescript
interface GenerationContext {
  enumValues: Record<string, string[]>
  getRandomForeignKey(tableName: string): string
  tableName: string
}

interface UniversalDataGenerator {
  generateForColumn(
    columnName: string,
    columnInfo: ColumnInfo,
    context: GenerationContext
  ): any
}

class IntelligentDataGenerator implements UniversalDataGenerator {
  // Universal patterns that work across ALL domains
  private universalPatterns = {
    // Identity patterns (universal)
    id: /^(id|uuid|guid)$/i,

    // Name patterns (universal)
    firstName: /^(first_name|fname|given_name)$/i,
    lastName: /^(last_name|lname|surname|family_name)$/i,
    fullName: /^(name|full_name|display_name|title)$/i,

    // Contact patterns (universal)
    email: /^(email|contact_email|user_email|mail)$/i,
    phone: /^(phone|telephone|mobile|cell|contact)$/i,
    website: /^(website|url|homepage|site)$/i,

    // Address patterns (universal)
    address: /^(address|street|street_address|location)$/i,
    city: /^(city|town|municipality)$/i,
    state: /^(state|province|region|territory)$/i,
    zipCode: /^(zip|zip_code|postal_code|postcode)$/i,
    country: /^(country|nation)$/i,

    // Temporal patterns (universal)
    createdAt: /^(created_at|created_date|date_created)$/i,
    updatedAt: /^(updated_at|modified_at|date_modified|last_updated)$/i,
    deletedAt: /^(deleted_at|date_deleted)$/i,
    birthDate: /^(birth_date|date_of_birth|dob|birthday)$/i,
    startDate: /^(start_date|date_start|begin_date)$/i,
    endDate: /^(end_date|date_end|finish_date)$/i,

    // Status patterns (universal)
    status: /^(status|state|condition)$/i,
    active: /^(active|enabled|is_active)$/i,

    // Descriptive patterns (universal)
    description: /^(description|desc|details|notes|comments)$/i,
    content: /^(content|body|text|message)$/i,

    // Numeric patterns (universal)
    amount: /^(amount|price|cost|fee|value|total)$/i,
    quantity: /^(quantity|qty|count|number)$/i,
    percentage: /^(percentage|percent|rate)$/i,

    // Metadata patterns (universal)
    metadata: /^(metadata|meta|data|settings|config)$/i,
    tags: /^(tags|labels|categories)$/i,

    // Foreign key patterns (universal)
    organizationId: /^(organization_id|org_id|company_id)$/i,
    userId: /^(user_id|created_by|updated_by|owner_id)$/i,
    parentId: /^(parent_id|parent)$/i,
  }

  generateForColumn(columnName: string, columnInfo: ColumnInfo, context: GenerationContext): any {
    // 1. Handle enums first (type-safe)
    if (columnInfo.type === 'enum' && columnInfo.enumValues) {
      return faker.helpers.arrayElement(columnInfo.enumValues)
    }

    // 2. Handle foreign keys (relationship-aware)
    if (columnInfo.foreignKey) {
      return context.getRandomForeignKey(columnInfo.foreignKey.table)
    }

    // 3. Handle JSON/JSONB with intelligent object generation
    if (columnInfo.type === 'json') {
      return this.generateJsonData(columnName, context)
    }

    // 4. Pattern-based generation (universal)
    for (const [patternName, regex] of Object.entries(this.universalPatterns)) {
      if (regex.test(columnName)) {
        return this.generateByPattern(patternName, columnInfo.type)
      }
    }

    // 5. Fallback to type-based generation
    return this.generateByType(columnInfo.type)
  }

  private generateByPattern(patternName: string, type: string): any {
    switch (patternName) {
      case 'firstName': return faker.person.firstName()
      case 'lastName': return faker.person.lastName()
      case 'fullName': return faker.person.fullName()
      case 'email': return faker.internet.email()
      case 'phone': return faker.phone.number()
      case 'address': return faker.location.streetAddress()
      case 'city': return faker.location.city()
      case 'state': return faker.location.state()
      case 'zipCode': return faker.location.zipCode()
      case 'website': return faker.internet.url()
      case 'createdAt': return faker.date.past().toISOString()
      case 'updatedAt': return faker.date.recent().toISOString()
      case 'amount': return faker.number.float({ min: 0, max: 10000, fractionDigits: 2 })
      default: return this.generateByType(type)
    }
  }

  private generateByType(type: string): any {
    switch (type) {
      case 'string': return faker.lorem.words(3)
      case 'number': return faker.number.int({ min: 1, max: 1000 })
      case 'boolean': return faker.datatype.boolean()
      case 'json': return { generated: true, timestamp: new Date().toISOString() }
      default: return null
    }
  }

  private generateJsonData(columnName: string, context: GenerationContext): Record<string, any> {
    // Smart JSON generation based on column name
    if (/settings|config|preferences/i.test(columnName)) {
      return {
        theme: faker.helpers.arrayElement(['light', 'dark', 'auto']),
        notifications: faker.datatype.boolean(),
        language: faker.helpers.arrayElement(['en', 'es', 'fr', 'de'])
      }
    }

    if (/metadata|meta/i.test(columnName)) {
      return {
        source: 'generated',
        timestamp: new Date().toISOString(),
        version: faker.system.semver()
      }
    }

    // Default JSON structure
    return {
      data: faker.lorem.words(5),
      created: new Date().toISOString()
    }
  }
}
```

### 4. Relationship Resolution Engine
```typescript
interface ForeignKeyInfo {
  column: string
  referenced_table: string
  referenced_column: string
}

class RelationshipResolver {
  // Build insertion order using topological sort
  buildInsertionOrder(dependencyGraph: Map<string, string[]>): string[] {
    const visited = new Set<string>()
    const visiting = new Set<string>()
    const result: string[] = []

    const visit = (table: string) => {
      if (visiting.has(table)) {
        throw new Error(`Circular dependency detected involving table: ${table}`)
      }
      if (visited.has(table)) return

      visiting.add(table)
      const dependencies = dependencyGraph.get(table) || []

      for (const dep of dependencies) {
        visit(dep)
      }

      visiting.delete(table)
      visited.add(table)
      result.push(table)
    }

    for (const table of dependencyGraph.keys()) {
      visit(table)
    }

    return result
  }

  // Resolve foreign key values during generation
  resolveForeignKey(
    referencedTable: string,
    generatedData: Map<string, any[]>
  ): string {
    const referencedRecords = generatedData.get(referencedTable)
    if (!referencedRecords || referencedRecords.length === 0) {
      throw new Error(`No records found for referenced table: ${referencedTable}`)
    }

    const randomRecord = faker.helpers.arrayElement(referencedRecords)
    return randomRecord.id
  }
}
```

### 5. Configuration System
```typescript
interface SeedConfig {
  // Record counts per table
  recordCounts: Record<string, number | { min: number; max: number }>;

  // Custom data generators per column
  columnOverrides: Record<string, (faker: Faker) => any>;

  // Relationship constraints
  relationships: {
    // Ensure certain relationships exist
    required: Array<{
      parentTable: string;
      childTable: string;
      ratio: number; // children per parent
    }>;
  };

  // Output configuration
  output: {
    format: 'sql' | 'json';
    directory: string;
    fileNaming: 'single' | 'per-table' | 'by-tier';
  };

  // Domain-specific generators
  dataGenerators: DataGenerator[];
}
```

### 6. Core Generator Class
```typescript
class SupaSeedGenerator {
  constructor(
    private supabase: SupabaseClient,
    private constants: { public: { Enums: Record<string, readonly string[]> } },
    private config: SeedConfig
  ) {}

  // Main generation method
  async generate(): Promise<void> {
    const analyzer = new RuntimeSchemaAnalyzer(this.supabase, this.constants)
    const resolver = new RelationshipResolver()
    const dataGen = new IntelligentDataGenerator()

    // 1. Analyze schema
    const dependencyGraph = await analyzer.buildDependencyGraph()
    const insertionOrder = resolver.buildInsertionOrder(dependencyGraph)

    // 2. Generate data in dependency order
    const generatedData = new Map<string, any[]>()

    for (const tableName of insertionOrder) {
      console.log(`Generating data for table: ${tableName}`)

      const tableData = await this.generateTableData(
        tableName,
        analyzer,
        resolver,
        dataGen,
        generatedData
      )

      generatedData.set(tableName, tableData)
    }

    // 3. Output SQL files
    await this.outputSQLFiles(generatedData)
  }

  private async generateTableData(
    tableName: string,
    analyzer: RuntimeSchemaAnalyzer,
    resolver: RelationshipResolver,
    dataGen: IntelligentDataGenerator,
    existingData: Map<string, any[]>
  ): Promise<any[]> {
    const columns = await analyzer.getTableSchema(tableName)
    const recordCount = this.getRecordCount(tableName)
    const records: any[] = []

    const context: GenerationContext = {
      enumValues: this.constants.public.Enums,
      getRandomForeignKey: (table: string) => resolver.resolveForeignKey(table, existingData),
      tableName
    }

    for (let i = 0; i < recordCount; i++) {
      const record: any = {}

      for (const column of columns) {
        if (column.name === 'id') {
          record.id = crypto.randomUUID()
        } else if (!column.hasDefault || !column.nullable) {
          record[column.name] = dataGen.generateForColumn(column.name, column, context)
        }
      }

      records.push(record)
    }

    return records
  }

  private getRecordCount(tableName: string): number {
    const count = this.config.recordCounts[tableName]
    if (typeof count === 'number') return count
    if (typeof count === 'object') {
      return faker.number.int({ min: count.min, max: count.max })
    }
    return 10 // default
  }

  private async outputSQLFiles(generatedData: Map<string, any[]>): Promise<void> {
    // Generate SQL INSERT statements and write to files
    // Implementation depends on config.output settings
  }
}
```

## Implementation Plan

### Phase 1: Core Infrastructure
1. **Type extraction utilities** - Parse Supabase generated types
2. **Schema analyzer** - Extract tables, columns, relationships
3. **Basic data generator** - Column name pattern matching
4. **Relationship resolver** - Dependency graph and FK resolution

### Phase 2: Smart Generation
1. **Intelligent data patterns** - Expand pattern matching
2. **JSON/JSONB handling** - Smart object generation
3. **Enum support** - Type-safe enum value generation
4. **Configuration system** - Flexible record counts and overrides

### Phase 3: Output & Integration
1. **SQL file generation** - Optimized INSERT statements
2. **File organization** - Multiple output formats
3. **CLI interface** - Easy command-line usage
4. **Integration helpers** - Package.json scripts

### Phase 4: Domain Extensions
1. **Healthcare generator** - Medical-specific data patterns
2. **E-commerce generator** - Product/order patterns
3. **Social media generator** - User/content patterns
4. **Plugin system** - Custom domain generators

## File Structure
```
scripts/
├── supaseed/
│   ├── core/
│   │   ├── schema-analyzer.ts
│   │   ├── relationship-resolver.ts
│   │   ├── data-generator.ts
│   │   └── sql-generator.ts
│   ├── generators/
│   │   ├── base-generator.ts
│   │   ├── healthcare-generator.ts
│   │   └── generic-generator.ts
│   ├── types/
│   │   ├── schema.ts
│   │   ├── config.ts
│   │   └── generator.ts
│   ├── utils/
│   │   ├── type-helpers.ts
│   │   └── faker-extensions.ts
│   └── index.ts
├── supaseed.config.ts
└── generate-seeds.ts
```

## Usage Example
```typescript
// supaseed.config.ts
import type { Database } from './packages/supabase-types/src/generated-types';
import { HealthcareDataGenerator } from './scripts/supaseed/generators/healthcare-generator';

export const config: SeedConfig = {
  recordCounts: {
    organizations: 5,
    patients: { min: 20, max: 50 },
    appointments: { min: 100, max: 200 }
  },
  dataGenerators: [new HealthcareDataGenerator()],
  output: {
    format: 'sql',
    directory: 'supabase/seeds/environments/development',
    fileNaming: 'by-tier'
  }
};

// generate-seeds.ts
import { SupaSeedGenerator } from './scripts/supaseed';
import type { Database } from './packages/supabase-types/src/generated-types';
import { config } from './supaseed.config';

const generator = new SupaSeedGenerator<Database>(config);
await generator.generate();
```

## Technical Requirements

### Dependencies
- `@faker-js/faker` - Data generation
- `typescript` - Type safety
- Node.js built-ins: `fs`, `path`

### Type Safety
- Full TypeScript support with generated Supabase types
- Generic constraints for schema validation
- Compile-time relationship validation

### Performance
- Efficient dependency resolution (topological sort)
- Batched SQL generation
- Memory-efficient for large datasets

### Extensibility
- Plugin architecture for domain-specific generators
- Configurable data patterns
- Custom column override system

## Success Criteria
1. ✅ Works with ANY Supabase schema without modification
2. ✅ Generates realistic, relationship-consistent data
3. ✅ Type-safe throughout the entire pipeline
4. ✅ Produces optimized SQL for fast seeding
5. ✅ Easily configurable for different use cases
6. ✅ Extensible for domain-specific needs

This system will be the definitive solution for Supabase seed data generation across any domain.
