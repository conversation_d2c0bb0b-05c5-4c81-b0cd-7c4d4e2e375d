import { supabase } from '#database';
import { getRandomElement, getRandomElements, logProgress, updateProgress } from '#helpers';
import { faker } from '@faker-js/faker';

export async function generateTier9PatientEngagement() {
  console.log('👥 Generating Tier 9: Patient Engagement...');
  
  try {
    // Get existing data for relationships
    const { data: patients } = await supabase.from('patients').select('id, organization_id');
    const { data: providers } = await supabase.from('healthcare_providers').select('id, organization_id');
    const { data: educationMaterials } = await supabase.from('education_materials').select('id, category');

    // Generate Patient Alerts
    console.log('Creating patient alerts...');
    const patientAlerts = [];
    
    for (const patient of patients.slice(0, Math.floor(patients.length * 0.3))) {
      const numAlerts = faker.number.int({ min: 0, max: 3 });
      for (let i = 0; i < numAlerts; i++) {
        const alertTypes = [
          'medication_interaction', 'allergy_warning', 'chronic_condition',
          'fall_risk', 'infection_control', 'lab_critical_value',
          'drug_allergy', 'contraindication', 'high_risk_patient'
        ];
        
        patientAlerts.push({
          patient_id: patient.id,
          alert_type: getRandomElement(alertTypes),
          severity: getRandomElement(['low', 'medium', 'high', 'critical']),
          description: getRandomElement([
            'Patient has multiple drug allergies - review before prescribing',
            'High fall risk due to medication side effects',
            'Critical lab values requiring immediate attention',
            'Chronic condition requires regular monitoring',
            'Drug interaction potential with current medications',
            'Patient requires isolation precautions',
            'Latex allergy - use latex-free equipment',
            'History of adverse drug reactions'
          ]),
          start_date: faker.date.past({ years: 1 }).toISOString().split('T')[0],
          end_date: faker.datatype.boolean({ probability: 0.3 }) ? 
            faker.date.future({ days: 90 }).toISOString().split('T')[0] : null,
          status: getRandomElement(['active', 'inactive', 'resolved']),
          created_by: getRandomElement(providers.filter(p => p.organization_id === patient.organization_id))?.id,
          metadata: {
            priority_level: faker.number.int({ min: 1, max: 5 }),
            auto_generated: faker.datatype.boolean(),
            acknowledgment_required: faker.datatype.boolean(),
            trigger_conditions: {
              medications: faker.datatype.boolean(),
              lab_values: faker.datatype.boolean(),
              vital_signs: faker.datatype.boolean()
            }
          }
        });
      }
    }
    
    if (patientAlerts.length > 0) {
      const { error } = await supabase.from('patient_alerts').insert(patientAlerts);
      if (error) throw error;
      logProgress(`✅ Created ${patientAlerts.length} patient alerts`);
    }

    // Generate Patient Questionnaires
    console.log('Creating patient questionnaires...');
    const questionnaires = [];
    const questionnaireTypes = [
      'health_assessment', 'pain_scale', 'depression_screening', 'anxiety_screening',
      'medication_adherence', 'lifestyle_assessment', 'surgical_prep', 'discharge_planning',
      'satisfaction_survey', 'wellness_check'
    ];
    
    for (const patient of patients) {
      const numQuestionnaires = faker.number.int({ min: 1, max: 4 });
      const selectedTypes = getRandomElements(questionnaireTypes, numQuestionnaires);
      
      for (const type of selectedTypes) {
        let responses = {};
        
        switch (type) {
          case 'health_assessment':
            responses = {
              general_health: getRandomElement(['excellent', 'very_good', 'good', 'fair', 'poor']),
              energy_level: faker.number.int({ min: 1, max: 10 }),
              sleep_quality: getRandomElement(['excellent', 'good', 'fair', 'poor']),
              stress_level: faker.number.int({ min: 1, max: 10 }),
              exercise_frequency: getRandomElement(['daily', 'weekly', 'monthly', 'rarely', 'never'])
            };
            break;
          case 'pain_scale':
            responses = {
              pain_level: faker.number.int({ min: 0, max: 10 }),
              pain_location: getRandomElement(['head', 'neck', 'back', 'chest', 'abdomen', 'extremities']),
              pain_type: getRandomElement(['sharp', 'dull', 'throbbing', 'burning', 'cramping']),
              pain_frequency: getRandomElement(['constant', 'intermittent', 'occasional']),
              pain_triggers: getRandomElements(['movement', 'weather', 'stress', 'eating', 'sleeping'], 2)
            };
            break;
          case 'depression_screening':
            responses = {
              phq9_score: faker.number.int({ min: 0, max: 27 }),
              mood_changes: faker.datatype.boolean(),
              sleep_changes: faker.datatype.boolean(),
              appetite_changes: faker.datatype.boolean(),
              energy_changes: faker.datatype.boolean(),
              concentration_issues: faker.datatype.boolean()
            };
            break;
          case 'medication_adherence':
            responses = {
              takes_as_prescribed: getRandomElement(['always', 'usually', 'sometimes', 'rarely', 'never']),
              missed_doses_week: faker.number.int({ min: 0, max: 14 }),
              reasons_for_missing: getRandomElements(['forgot', 'side_effects', 'cost', 'feeling_better'], 2),
              uses_reminders: faker.datatype.boolean(),
              understands_instructions: faker.datatype.boolean()
            };
            break;
          default:
            responses = {
              overall_rating: faker.number.int({ min: 1, max: 5 }),
              satisfaction_level: getRandomElement(['very_satisfied', 'satisfied', 'neutral', 'dissatisfied']),
              would_recommend: faker.datatype.boolean(),
              additional_comments: faker.lorem.sentence()
            };
        }
        
        questionnaires.push({
          patient_id: patient.id,
          questionnaire_type: type,
          responses: responses,
          completed_at: faker.datatype.boolean({ probability: 0.8 }) ? 
            faker.date.recent({ days: 30 }).toISOString() : null,
          metadata: {
            version: '1.0',
            language: 'en',
            completion_time_minutes: faker.number.int({ min: 5, max: 30 }),
            device_type: getRandomElement(['mobile', 'tablet', 'desktop']),
            ip_address: faker.internet.ip()
          }
        });
      }
    }
    
    if (questionnaires.length > 0) {
      const { error } = await supabase.from('patient_questionnaires').insert(questionnaires);
      if (error) throw error;
      logProgress(`✅ Created ${questionnaires.length} patient questionnaires`);
    }

    // Generate Patient Education Records
    console.log('Creating patient education records...');
    const educationRecords = [];
    
    for (const patient of patients.slice(0, Math.floor(patients.length * 0.6))) {
      const numRecords = faker.number.int({ min: 1, max: 5 });
      const selectedMaterials = getRandomElements(educationMaterials, Math.min(numRecords, educationMaterials.length));
      
      for (const material of selectedMaterials) {
        educationRecords.push({
          patient_id: patient.id,
          material_id: material.id,
          provider_id: getRandomElement(providers.filter(p => p.organization_id === patient.organization_id))?.id,
          provided_date: faker.date.recent({ days: 90 }).toISOString().split('T')[0],
          notes: getRandomElement([
            'Patient demonstrated understanding of material',
            'Patient has questions about treatment plan',
            'Follow-up education needed',
            'Patient expressed concerns about side effects',
            'Material provided for family review',
            'Patient requested additional resources'
          ]),
          metadata: {
            delivery_method: getRandomElement(['verbal', 'written', 'video', 'interactive']),
            patient_understanding: getRandomElement(['excellent', 'good', 'fair', 'poor']),
            follow_up_needed: faker.datatype.boolean(),
            language_preference: getRandomElement(['english', 'spanish', 'other']),
            accessibility_needs: {
              large_print: faker.datatype.boolean({ probability: 0.1 }),
              audio_version: faker.datatype.boolean({ probability: 0.1 }),
              interpreter_needed: faker.datatype.boolean({ probability: 0.05 })
            }
          }
        });
      }
    }
    
    if (educationRecords.length > 0) {
      const { error } = await supabase.from('patient_education_records').insert(educationRecords);
      if (error) throw error;
      logProgress(`✅ Created ${educationRecords.length} patient education records`);
    }

    // Generate Patient Portal Settings
    console.log('Creating patient portal settings...');
    const portalSettings = [];
    
    for (const patient of patients) {
      portalSettings.push({
        patient_id: patient.id,
        preferences: {
          language: getRandomElement(['en', 'es', 'fr', 'de']),
          timezone: getRandomElement([
            'America/New_York', 'America/Chicago', 'America/Denver', 
            'America/Los_Angeles', 'America/Phoenix'
          ]),
          date_format: getRandomElement(['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD']),
          time_format: getRandomElement(['12h', '24h']),
          theme: getRandomElement(['light', 'dark', 'auto']),
          font_size: getRandomElement(['small', 'medium', 'large']),
          accessibility: {
            high_contrast: faker.datatype.boolean({ probability: 0.1 }),
            screen_reader: faker.datatype.boolean({ probability: 0.05 }),
            keyboard_navigation: faker.datatype.boolean({ probability: 0.1 })
          }
        },
        communication_settings: {
          email_notifications: faker.datatype.boolean({ probability: 0.8 }),
          sms_notifications: faker.datatype.boolean({ probability: 0.6 }),
          phone_calls: faker.datatype.boolean({ probability: 0.4 }),
          secure_messaging: faker.datatype.boolean({ probability: 0.9 }),
          appointment_reminders: {
            email: faker.datatype.boolean({ probability: 0.8 }),
            sms: faker.datatype.boolean({ probability: 0.7 }),
            days_before: getRandomElement([1, 2, 3, 7])
          },
          lab_results: {
            immediate_notification: faker.datatype.boolean({ probability: 0.6 }),
            email_summary: faker.datatype.boolean({ probability: 0.8 }),
            critical_only: faker.datatype.boolean({ probability: 0.2 })
          },
          prescription_updates: {
            refill_reminders: faker.datatype.boolean({ probability: 0.8 }),
            new_prescriptions: faker.datatype.boolean({ probability: 0.9 }),
            interaction_alerts: faker.datatype.boolean({ probability: 0.7 })
          }
        },
        metadata: {
          last_login: faker.date.recent({ days: 30 }).toISOString(),
          login_count: faker.number.int({ min: 1, max: 50 }),
          setup_completed: faker.datatype.boolean({ probability: 0.9 }),
          security_questions_set: faker.datatype.boolean({ probability: 0.8 }),
          two_factor_enabled: faker.datatype.boolean({ probability: 0.3 }),
          gdpr_consent: faker.datatype.boolean({ probability: 0.95 }),
          marketing_consent: faker.datatype.boolean({ probability: 0.4 })
        }
      });
    }
    
    if (portalSettings.length > 0) {
      const { error } = await supabase.from('patient_portal_settings').insert(portalSettings);
      if (error) throw error;
      logProgress(`✅ Created ${portalSettings.length} patient portal settings`);
    }

    updateProgress('tier9', 'completed');
    console.log('✅ Tier 9: Patient Engagement completed successfully!');
    
  } catch (error) {
    console.error('❌ Error in Tier 9:', error);
    throw error;
  }
} 