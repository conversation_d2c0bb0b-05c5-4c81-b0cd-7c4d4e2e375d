import { supabase } from '#database';
import { getRandomElement, logProgress, updateProgress } from '#helpers';
import { faker } from '@faker-js/faker';

export async function generateTier11Workflows() {
  console.log('🔄 Generating Tier 11: Workflows & Business Processes...');
  
  try {
    // Get existing data for relationships
    const { data: organizations } = await supabase.from('organizations').select('id, name');
    const { data: providers } = await supabase.from('healthcare_providers').select('id, organization_id, first_name, last_name');
    const { data: patients } = await supabase.from('patients').select('id, organization_id');

    // Generate Workflows
    console.log('Creating workflows...');
    const workflows = [];
    const workflowTypes = [
      'appointment_reminder', 'lab_result_notification', 'prescription_renewal',
      'patient_followup', 'referral_management', 'insurance_verification', 'document_review'
    ];
    
    for (const organization of organizations) {
      for (const workflowType of workflowTypes) {
        const steps = generateWorkflowSteps(workflowType);
        const triggerConfig = generateTriggerConfig(workflowType);
        
        workflows.push({
          organization_id: organization.id,
          name: `${workflowType.replace('_', ' ').toUpperCase()} Workflow`,
          type: workflowType,
          description: getWorkflowDescription(workflowType),
          trigger_type: getRandomElement(['scheduled', 'event_based', 'manual']),
          trigger_config: triggerConfig,
          steps: steps,
          enabled: faker.datatype.boolean({ probability: 0.8 }),
          created_by: getRandomElement(providers.filter(p => p.organization_id === organization.id))?.id,
          metadata: {
            version: '1.0',
            last_executed: faker.date.recent({ days: 7 }).toISOString(),
            execution_count: faker.number.int({ min: 0, max: 100 }),
            success_rate: faker.number.float({ min: 0.7, max: 1.0, multipleOf: 0.01 }),
            average_duration_minutes: faker.number.int({ min: 5, max: 120 }),
            compliance_required: faker.datatype.boolean({ probability: 0.6 }),
            approval_required: faker.datatype.boolean({ probability: 0.3 }),
            error_handling: {
              retry_attempts: faker.number.int({ min: 1, max: 5 }),
              escalation_required: faker.datatype.boolean({ probability: 0.4 }),
              notification_on_failure: faker.datatype.boolean({ probability: 0.9 })
            }
          }
        });
      }
    }
    
    if (workflows.length > 0) {
      const { error } = await supabase.from('workflows').insert(workflows);
      if (error) throw error;
      logProgress(`✅ Created ${workflows.length} workflows`);
    }

    // Get created workflows for instances
    const { data: createdWorkflows } = await supabase.from('workflows').select('id, organization_id, type, steps');

    // Generate Workflow Instances
    console.log('Creating workflow instances...');
    const workflowInstances = [];
    
    for (const workflow of createdWorkflows) {
      const numInstances = faker.number.int({ min: 5, max: 20 });
      const orgPatients = patients.filter(p => p.organization_id === workflow.organization_id);
      const orgProviders = providers.filter(p => p.organization_id === workflow.organization_id);
      
      for (let i = 0; i < numInstances; i++) {
        const status = getRandomElement(['pending', 'in_progress', 'completed', 'cancelled', 'failed']);
        const startedAt = faker.date.recent({ days: 30 });
        const context = generateWorkflowContext(workflow.type, orgPatients, orgProviders);
        
        let completedAt = null;
        let currentStep = null;
        
        if (status === 'completed') {
          completedAt = faker.date.between({ from: startedAt, to: new Date() });
          currentStep = workflow.steps.length;
        } else if (status === 'in_progress') {
          currentStep = faker.number.int({ min: 1, max: workflow.steps.length - 1 });
        } else if (status === 'failed' || status === 'cancelled') {
          currentStep = faker.number.int({ min: 1, max: workflow.steps.length });
        }
        
        workflowInstances.push({
          workflow_id: workflow.id,
          status: status,
          context: context,
          current_step: currentStep,
          started_at: startedAt.toISOString(),
          completed_at: completedAt?.toISOString() || null,
          results: status === 'completed' ? {
            success: true,
            execution_time_minutes: faker.number.int({ min: 5, max: 60 }),
            steps_completed: workflow.steps.length,
            notifications_sent: faker.number.int({ min: 0, max: 5 }),
            documents_generated: faker.number.int({ min: 0, max: 3 })
          } : null,
          metadata: {
            priority: getRandomElement(['low', 'medium', 'high', 'urgent']),
            triggered_by: getRandomElement(['system', 'user', 'schedule', 'event']),
            retry_count: status === 'failed' ? faker.number.int({ min: 0, max: 3 }) : 0,
            estimated_completion: faker.date.future({ days: 1 }).toISOString(),
            resource_usage: {
              cpu_time: faker.number.float({ min: 0.1, max: 5.0, multipleOf: 0.1 }),
              memory_usage: faker.number.int({ min: 10, max: 500 }),
              api_calls: faker.number.int({ min: 1, max: 20 })
            }
          }
        });
      }
    }
    
    if (workflowInstances.length > 0) {
      const { error } = await supabase.from('workflow_instances').insert(workflowInstances);
      if (error) throw error;
      logProgress(`✅ Created ${workflowInstances.length} workflow instances`);
    }

    // Get created workflow instances for logs
    const { data: createdInstances } = await supabase.from('workflow_instances').select('id, workflow_id, current_step, status');

    // Generate Workflow Logs
    console.log('Creating workflow logs...');
    const workflowLogs = [];
    
    for (const instance of createdInstances) {
      const workflow = createdWorkflows.find(w => w.id === instance.workflow_id);
      if (!workflow) continue;
      
      const stepsToLog = instance.current_step || 1;
      
      for (let stepNum = 1; stepNum <= stepsToLog; stepNum++) {
        const step = workflow.steps[stepNum - 1];
        if (!step) continue;
        
        const stepStatus = stepNum < stepsToLog ? 'completed' : 
          (instance.status === 'failed' ? 'failed' : 
           instance.status === 'cancelled' ? 'cancelled' : 
           instance.status === 'in_progress' ? 'in_progress' : 'completed');
        
        workflowLogs.push({
          workflow_instance_id: instance.id,
          step_number: stepNum,
          step_name: step.name || `Step ${stepNum}`,
          status: stepStatus,
          message: generateStepMessage(stepStatus, step.name),
          details: {
            step_type: step.type || 'action',
            duration_seconds: faker.number.int({ min: 1, max: 300 }),
            input_data: step.input || {},
            output_data: stepStatus === 'completed' ? generateStepOutput(step.type) : null,
            error_details: stepStatus === 'failed' ? {
              error_code: faker.string.alphanumeric({ length: 6, casing: 'upper' }),
              error_message: faker.lorem.sentence(),
              stack_trace: faker.lorem.lines(3)
            } : null
          }
        });
      }
    }
    
    if (workflowLogs.length > 0) {
      const { error } = await supabase.from('workflow_logs').insert(workflowLogs);
      if (error) throw error;
      logProgress(`✅ Created ${workflowLogs.length} workflow logs`);
    }

    // Generate Organization Invites
    console.log('Creating organization invites...');
    const organizationInvites = [];
    
    for (const organization of organizations) {
      const numInvites = faker.number.int({ min: 2, max: 10 });
      const invitingProvider = getRandomElement(providers.filter(p => p.organization_id === organization.id));
      
      for (let i = 0; i < numInvites; i++) {
        const status = getRandomElement(['pending', 'accepted', 'declined', 'expired']);
        
({
          organization_id: organization.id,
          email: faker.internet.email(),
          role: getRandomElement([
            'physician', 'nurse_practitioner', 'registered_nurse', 'medical_assistant',
            'front_desk', 'billing_staff', 'clinical_admin'
          ]),
          invited_by: invitingProvider?.id,
          status: status,
          expires_at: faker.date.future({ days: 7 }).toISOString(),
          metadata: {
            invitation_message: faker.lorem.paragraph(),
            department_assignment: getRandomElement([
              'primary_care', 'emergency', 'cardiology', 'neurology', 'administration'
            ]),
            access_level: getRandomElement(['basic', 'intermediate', 'advanced', 'full']),
            temporary_access: faker.datatype.boolean({ probability: 0.2 }),
            background_check_required: faker.datatype.boolean({ probability: 0.8 }),
            orientation_required: faker.datatype.boolean({ probability: 0.9 }),
            mentor_assigned: faker.datatype.boolean({ probability: 0.6 }) ? 
              getRandomElement(providers.filter(p => p.organization_id === organization.id))?.id : null,
            start_date: faker.date.future({ days: 14 }).toISOString().split('T')[0],
            probation_period_days: faker.number.int({ min: 30, max: 180 })
          }
        });
      }
    }
    
    if (organizationInvites.length > 0) {
      const { error } = await supabase.from('organization_invites').insert(organizationInvites);
      if (error) throw error;
      logProgress(`✅ Created ${organizationInvites.length} organization invites`);
    }

    updateProgress('tier11', 'completed');
    console.log('✅ Tier 11: Workflows & Business Processes completed successfully!');
    
  } catch (error) {
    console.error('❌ Error in Tier 11:', error);
    throw error;
  }
}

function generateWorkflowSteps(workflowType) {
  const stepTemplates = {
    appointment_reminder: [
      { name: 'Check Upcoming Appointments', type: 'query', input: { timeframe: '24_hours' } },
      { name: 'Validate Patient Contact Info', type: 'validation', input: { required_fields: ['phone', 'email'] } },
      { name: 'Send Reminder Notification', type: 'notification', input: { channels: ['sms', 'email'] } },
      { name: 'Log Reminder Activity', type: 'audit', input: { action: 'reminder_sent' } }
    ],
    lab_result_notification: [
      { name: 'Detect New Lab Results', type: 'trigger', input: { source: 'lab_system' } },
      { name: 'Identify Critical Values', type: 'analysis', input: { check_ranges: true } },
      { name: 'Notify Provider', type: 'notification', input: { urgency: 'high' } },
      { name: 'Update Patient Record', type: 'update', input: { link_to_appointment: true } }
    ],
    prescription_renewal: [
      { name: 'Check Expiring Prescriptions', type: 'query', input: { days_ahead: 7 } },
      { name: 'Verify Patient Eligibility', type: 'validation', input: { insurance_active: true } },
      { name: 'Request Provider Approval', type: 'approval', input: { timeout_hours: 48 } },
      { name: 'Process Renewal', type: 'action', input: { send_to_pharmacy: true } }
    ]
  };
  
  return stepTemplates[workflowType] || [
    { name: 'Initialize Workflow', type: 'init', input: {} },
    { name: 'Process Data', type: 'process', input: {} },
    { name: 'Complete Workflow', type: 'complete', input: {} }
  ];
}

function generateTriggerConfig(workflowType) {
  const configs = {
    appointment_reminder: {
      schedule: 'daily',
      time: '09:00',
      timezone: 'America/New_York',
      conditions: { appointment_status: 'scheduled' }
    },
    lab_result_notification: {
      event: 'lab_result_received',
      filters: { critical_values: true },
      immediate: true
    },
    prescription_renewal: {
      schedule: 'daily',
      time: '08:00',
      conditions: { days_until_expiry: 7 }
    }
  };
  
  return configs[workflowType] || {
    trigger_type: 'manual',
    conditions: {}
  };
}

function getWorkflowDescription(workflowType) {
  const descriptions = {
    appointment_reminder: 'Automatically sends appointment reminders to patients 24 hours before their scheduled visits',
    lab_result_notification: 'Notifies healthcare providers when critical lab results are received',
    prescription_renewal: 'Manages the prescription renewal process including provider approval',
    patient_followup: 'Ensures timely follow-up care for patients with chronic conditions',
    referral_management: 'Tracks and manages patient referrals to specialists',
    insurance_verification: 'Verifies patient insurance coverage before appointments',
    document_review: 'Routes medical documents for provider review and approval'
  };
  
  return descriptions[workflowType] || `Automated workflow for ${workflowType.replace('_', ' ')}`;
}

function generateWorkflowContext(workflowType, patients, providers) {
  const contexts = {
    appointment_reminder: {
      patient_id: getRandomElement(patients)?.id,
      appointment_date: faker.date.future({ days: 1 }).toISOString(),
      provider_id: getRandomElement(providers)?.id,
      notification_preferences: ['sms', 'email']
    },
    lab_result_notification: {
      patient_id: getRandomElement(patients)?.id,
      lab_test: getRandomElement(['CBC', 'BMP', 'Lipid Panel']),
      critical_value: faker.datatype.boolean({ probability: 0.3 }),
      provider_id: getRandomElement(providers)?.id
    },
    prescription_renewal: {
      patient_id: getRandomElement(patients)?.id,
      medication: faker.lorem.word(),
      prescribing_provider: getRandomElement(providers)?.id,
      pharmacy_id: faker.string.uuid()
    }
  };
  
  return contexts[workflowType] || {
    entity_id: getRandomElement(patients)?.id,
    entity_type: 'patient',
    initiator: getRandomElement(providers)?.id
  };
}

function generateStepMessage(status, stepName) {
  const messages = {
    completed: `${stepName} completed successfully`,
    failed: `${stepName} failed to execute`,
    cancelled: `${stepName} was cancelled`,
    in_progress: `${stepName} is currently running`,
    pending: `${stepName} is waiting to execute`
  };
  
  return messages[status] || `${stepName} status: ${status}`;
}

function generateStepOutput(stepType) {
  const outputs = {
    query: { records_found: faker.number.int({ min: 0, max: 100 }) },
    validation: { valid: faker.datatype.boolean({ probability: 0.8 }) },
    notification: { sent: true, delivery_id: faker.string.uuid() },
    audit: { logged: true, log_id: faker.string.uuid() },
    action: { success: true, transaction_id: faker.string.uuid() }
  };
  
  return outputs[stepType] || { result: 'success' };
} 