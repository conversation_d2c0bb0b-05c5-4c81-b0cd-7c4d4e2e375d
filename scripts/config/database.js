import { createClient } from '@supabase/supabase-js';

// Local Supabase configuration for development
const SUPABASE_URL = 'http://127.0.0.1:54321';
// Use service role key for seed operations to bypass RLS policies
const SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

// Initialize Supabase client with service role for administrative operations
export const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY); 