import { faker } from '@faker-js/faker';
import { mkdirSync, writeFileSync } from 'fs';
import { join } from 'path';
import { supabase } from '../config/database.js';
import { getRandomElement, getRandomElements, logProgress, updateProgress } from '../config/helpers.js';

interface Patient {
  id: string;
  organization_id: string;
}

interface Provider {
  id: string;
  organization_id: string;
}

interface EducationMaterial {
  id: string;
  category: string;
}

interface PatientAlert {
  patient_id: string;
  alert_type: string;
  severity: string;
  description: string;
  start_date: string;
  end_date: string | null;
  status: string;
  created_by: string | null;
  metadata: Record<string, any>;
}

interface PatientQuestionnaire {
  patient_id: string;
  questionnaire_type: string;
  responses: Record<string, any>;
  completed_at: string | null;
  metadata: Record<string, any>;
}

interface PatientEducationRecord {
  patient_id: string;
  material_id: string;
  provider_id: string | null;
  provided_date: string;
  notes: string;
  metadata: Record<string, any>;
}

interface PatientPortalSettings {
  patient_id: string;
  preferences: Record<string, any>;
  communication_settings: Record<string, any>;
  metadata: Record<string, any>;
}

export async function generateTier9PatientEngagement(): Promise<void> {
  console.log('👥 Generating Tier 9: Patient Engagement SQL...');

  try {
    // Get existing data for relationships
    const { data: patients } = await supabase.from('patients').select('id, organization_id');
    const { data: providers } = await supabase.from('healthcare_providers').select('id, organization_id');
    const { data: educationMaterials } = await supabase.from('education_materials').select('id, category');

    if (!patients || !providers || !educationMaterials) {
      throw new Error('Failed to fetch required data for patient engagement generation');
    }

    // Generate SQL for each table
    const patientAlertsSql = generatePatientAlertsSQL(patients, providers);
    const questionnairesSql = generatePatientQuestionnairesSQL(patients);
    const educationRecordsSql = generatePatientEducationRecordsSQL(patients, providers, educationMaterials);
    const portalSettingsSql = generatePatientPortalSettingsSQL(patients);

    // Combine all SQL
    const fullSql = `
-- Tier 9: Patient Engagement
-- Generated on ${new Date().toISOString()}

BEGIN;

${patientAlertsSql}

${questionnairesSql}

${educationRecordsSql}

${portalSettingsSql}

COMMIT;
`;

    // Write to file
    const outputDir = join(process.cwd(), 'supabase', 'seeds', 'environments', 'development');
    mkdirSync(outputDir, { recursive: true });

    const outputFile = join(outputDir, 'generated_tier9_patient_engagement.sql');
    writeFileSync(outputFile, fullSql);

    console.log(`✅ Generated SQL file: ${outputFile}`);
    updateProgress('tier9', 'completed');

  } catch (error) {
    console.error('❌ Error in Tier 9:', error);
    throw error;
  }
}

function generatePatientAlertsSQL(patients: Patient[], providers: Provider[]): string {
  console.log('Creating patient alerts SQL...');

  const patientAlerts: PatientAlert[] = [];

  for (const patient of patients.slice(0, Math.floor(patients.length * 0.3))) {
    const numAlerts = faker.number.int({ min: 0, max: 3 });
    for (let i = 0; i < numAlerts; i++) {
      const alertTypes = [
        'medication_interaction', 'allergy_warning', 'chronic_condition',
        'fall_risk', 'infection_control', 'lab_critical_value',
        'drug_allergy', 'contraindication', 'high_risk_patient'
      ];

      const orgProviders = providers.filter(p => p.organization_id === patient.organization_id);

      patientAlerts.push({
        patient_id: patient.id,
        alert_type: getRandomElement(alertTypes),
        severity: getRandomElement(['low', 'medium', 'high', 'critical']),
        description: getRandomElement([
          'Patient has multiple drug allergies - review before prescribing',
          'High fall risk due to medication side effects',
          'Critical lab values requiring immediate attention',
          'Chronic condition requires regular monitoring',
          'Drug interaction potential with current medications',
          'Patient requires isolation precautions',
          'Latex allergy - use latex-free equipment',
          'History of adverse drug reactions'
        ]),
        start_date: faker.date.past({ years: 1 }).toISOString().split('T')[0],
        end_date: faker.datatype.boolean({ probability: 0.3 }) ?
          faker.date.future({ days: 90 }).toISOString().split('T')[0] : null,
        status: getRandomElement(['active', 'inactive', 'resolved']),
        created_by: orgProviders.length > 0 ? getRandomElement(orgProviders).id : null,
        metadata: {
          priority_level: faker.number.int({ min: 1, max: 5 }),
          auto_generated: faker.datatype.boolean(),
          acknowledgment_required: faker.datatype.boolean(),
          trigger_conditions: {
            medications: faker.datatype.boolean(),
            lab_values: faker.datatype.boolean(),
            vital_signs: faker.datatype.boolean()
          }
        }
      });
    }
  }

  if (patientAlerts.length === 0) {
    return '-- No patient alerts to insert\n';
  }

  let sql = '-- Patient Alerts\nINSERT INTO public.patient_alerts (\n';
  sql += '  id, patient_id, alert_type, severity, description, start_date, end_date, status, created_by, metadata\n';
  sql += ') VALUES\n';

  const values = patientAlerts.map(alert => {
    const endDate = alert.end_date ? `'${alert.end_date}'` : 'NULL';
    const createdBy = alert.created_by ? `'${alert.created_by}'` : 'NULL';
    return `  (uuid_generate_v4(), '${alert.patient_id}', '${alert.alert_type}', '${alert.severity}', '${alert.description.replace(/'/g, "''")}', '${alert.start_date}', ${endDate}, '${alert.status}', ${createdBy}, '${JSON.stringify(alert.metadata).replace(/'/g, "''")}'::jsonb)`;
  });

  sql += values.join(',\n') + ';\n\n';

  logProgress(`✅ Generated SQL for ${patientAlerts.length} patient alerts`);
  return sql;
}

function generatePatientQuestionnairesSQL(patients: Patient[]): string {
  console.log('Creating patient questionnaires SQL...');

  const questionnaires: PatientQuestionnaire[] = [];
  const questionnaireTypes = [
    'health_assessment', 'pain_scale', 'depression_screening', 'anxiety_screening',
    'medication_adherence', 'lifestyle_assessment', 'surgical_prep', 'discharge_planning',
    'satisfaction_survey', 'wellness_check'
  ];

  for (const patient of patients) {
    const numQuestionnaires = faker.number.int({ min: 1, max: 4 });
    const selectedTypes = getRandomElements(questionnaireTypes, numQuestionnaires);

    for (const type of selectedTypes) {
      let responses: Record<string, any> = {};

      switch (type) {
        case 'health_assessment':
          responses = {
            general_health: getRandomElement(['excellent', 'very_good', 'good', 'fair', 'poor']),
            energy_level: faker.number.int({ min: 1, max: 10 }),
            sleep_quality: getRandomElement(['excellent', 'good', 'fair', 'poor']),
            stress_level: faker.number.int({ min: 1, max: 10 }),
            exercise_frequency: getRandomElement(['daily', 'weekly', 'monthly', 'rarely', 'never'])
          };
          break;
        case 'pain_scale':
          responses = {
            pain_level: faker.number.int({ min: 0, max: 10 }),
            pain_location: getRandomElement(['head', 'neck', 'back', 'chest', 'abdomen', 'extremities']),
            pain_type: getRandomElement(['sharp', 'dull', 'throbbing', 'burning', 'cramping']),
            pain_frequency: getRandomElement(['constant', 'intermittent', 'occasional']),
            pain_triggers: getRandomElements(['movement', 'weather', 'stress', 'eating', 'sleeping'], 2)
          };
          break;
        case 'depression_screening':
          responses = {
            phq9_score: faker.number.int({ min: 0, max: 27 }),
            mood_changes: faker.datatype.boolean(),
            sleep_changes: faker.datatype.boolean(),
            appetite_changes: faker.datatype.boolean(),
            energy_changes: faker.datatype.boolean(),
            concentration_issues: faker.datatype.boolean()
          };
          break;
        case 'medication_adherence':
          responses = {
            takes_as_prescribed: getRandomElement(['always', 'usually', 'sometimes', 'rarely', 'never']),
            missed_doses_week: faker.number.int({ min: 0, max: 14 }),
            reasons_for_missing: getRandomElements(['forgot', 'side_effects', 'cost', 'feeling_better'], 2),
            uses_reminders: faker.datatype.boolean(),
            understands_instructions: faker.datatype.boolean()
          };
          break;
        default:
          responses = {
            overall_rating: faker.number.int({ min: 1, max: 5 }),
            satisfaction_level: getRandomElement(['very_satisfied', 'satisfied', 'neutral', 'dissatisfied']),
            would_recommend: faker.datatype.boolean(),
            additional_comments: faker.lorem.sentence()
          };
      }

      questionnaires.push({
        patient_id: patient.id,
        questionnaire_type: type,
        responses: responses,
        completed_at: faker.datatype.boolean({ probability: 0.8 }) ?
          faker.date.recent({ days: 30 }).toISOString() : null,
        metadata: {
          version: '1.0',
          language: 'en',
          completion_time_minutes: faker.number.int({ min: 5, max: 30 }),
          device_type: getRandomElement(['mobile', 'tablet', 'desktop']),
          ip_address: faker.internet.ip()
        }
      });
    }
  }

  if (questionnaires.length === 0) {
    return '-- No patient questionnaires to insert\n';
  }

  let sql = '-- Patient Questionnaires\nINSERT INTO public.patient_questionnaires (\n';
  sql += '  id, patient_id, questionnaire_type, responses, completed_at, metadata\n';
  sql += ') VALUES\n';

  const values = questionnaires.map(q => {
    const completedAt = q.completed_at ? `'${q.completed_at}'` : 'NULL';
    return `  (uuid_generate_v4(), '${q.patient_id}', '${q.questionnaire_type}', '${JSON.stringify(q.responses).replace(/'/g, "''")}'::jsonb, ${completedAt}, '${JSON.stringify(q.metadata).replace(/'/g, "''")}'::jsonb)`;
  });

  sql += values.join(',\n') + ';\n\n';

  logProgress(`✅ Generated SQL for ${questionnaires.length} patient questionnaires`);
  return sql;
}
