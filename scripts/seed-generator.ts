#!/usr/bin/env node

import { faker } from '@faker-js/faker';
import { writeFileSync, mkdirSync } from 'fs';
import { join } from 'path';
import type { Database } from '../packages/supabase-types/src/generated-types.js';

// Type aliases for easier use
type Tables = Database['public']['Tables'];
type Enums = Database['public']['Enums'];

// Extract Insert types for each table
type OrganizationInsert = Tables['organizations']['Insert'];
type FacilityInsert = Tables['facilities']['Insert'];
type DepartmentInsert = Tables['departments']['Insert'];
type HealthcareProviderInsert = Tables['healthcare_providers']['Insert'];
type PatientInsert = Tables['patients']['Insert'];
type AppointmentInsert = Tables['appointments']['Insert'];
type AllergyInsert = Tables['allergies']['Insert'];

// Configuration for data generation
const CONFIG = {
  ORGANIZATIONS: 5,
  FACILITIES_PER_ORG: 2,
  DEPARTMENTS_PER_FACILITY: 3,
  PROVIDERS_PER_ORG: 8,
  PATIENTS_PER_ORG: 25,
  APPOINTMENTS_PER_ORG: 15,
  ALLERGIES_PERCENTAGE: 0.4, // 40% of patients have allergies
};

interface GeneratedData {
  organizations: OrganizationInsert[];
  facilities: FacilityInsert[];
  departments: DepartmentInsert[];
  healthcareProviders: HealthcareProviderInsert[];
  patients: PatientInsert[];
  appointments: AppointmentInsert[];
  allergies: AllergyInsert[];
}

class TypedSeedGenerator {
  private data: GeneratedData = {
    organizations: [],
    facilities: [],
    departments: [],
    healthcareProviders: [],
    patients: [],
    appointments: [],
    allergies: [],
  };

  async generate(): Promise<void> {
    console.log('🚀 Starting TypeScript-based Seed Generation...');
    console.log('📊 Using Supabase Generated Types for Type Safety');

    // Generate data in dependency order
    this.generateOrganizations();
    this.generateFacilities();
    this.generateDepartments();
    this.generateHealthcareProviders();
    this.generatePatients();
    this.generateAppointments();
    this.generateAllergies();

    // Generate SQL files
    this.generateSQLFiles();

    console.log('✅ Seed generation completed successfully!');
  }

  private generateOrganizations(): void {
    console.log('🏢 Generating Organizations...');
    
    const organizationTypes: Array<Tables['organizations']['Row']['type']> = [
      'hospital', 'clinic', 'practice', 'health_system'
    ];

    for (let i = 0; i < CONFIG.ORGANIZATIONS; i++) {
      const name = faker.company.name() + ' ' + faker.helpers.arrayElement(['Medical Center', 'Hospital', 'Clinic', 'Health System']);
      
      this.data.organizations.push({
        name,
        type: faker.helpers.arrayElement(organizationTypes),
        address: faker.location.streetAddress(),
        city: faker.location.city(),
        state: faker.location.state(),
        zip_code: faker.location.zipCode(),
        phone: faker.phone.number(),
        email: faker.internet.email(),
        website: faker.internet.url(),
        settings: {
          timezone: faker.helpers.arrayElement(['America/New_York', 'America/Chicago', 'America/Denver', 'America/Los_Angeles']),
          business_hours: {
            monday: { open: '08:00', close: '17:00' },
            tuesday: { open: '08:00', close: '17:00' },
            wednesday: { open: '08:00', close: '17:00' },
            thursday: { open: '08:00', close: '17:00' },
            friday: { open: '08:00', close: '17:00' },
            saturday: { open: '09:00', close: '13:00' },
            sunday: { closed: true }
          },
          features: {
            patient_portal: faker.datatype.boolean({ probability: 0.8 }),
            online_scheduling: faker.datatype.boolean({ probability: 0.7 }),
            telemedicine: faker.datatype.boolean({ probability: 0.6 })
          }
        }
      });
    }

    console.log(`✅ Generated ${this.data.organizations.length} organizations`);
  }

  private generateFacilities(): void {
    console.log('🏥 Generating Facilities...');

    for (const org of this.data.organizations) {
      const facilityCount = faker.number.int({ min: 1, max: CONFIG.FACILITIES_PER_ORG });
      
      for (let i = 0; i < facilityCount; i++) {
        const facilityTypes: Array<Tables['facilities']['Row']['type']> = [
          'main_campus', 'satellite_clinic', 'urgent_care', 'specialty_clinic'
        ];

        this.data.facilities.push({
          organization_id: `org_${this.data.organizations.indexOf(org)}`, // Will be replaced with actual UUID in SQL
          name: `${org.name} - ${faker.helpers.arrayElement(['Main Campus', 'North Campus', 'South Campus', 'East Wing', 'West Wing'])}`,
          type: faker.helpers.arrayElement(facilityTypes),
          address: faker.location.streetAddress(),
          city: faker.location.city(),
          state: faker.location.state(),
          zip_code: faker.location.zipCode(),
          phone: faker.phone.number(),
          metadata: {
            capacity: faker.number.int({ min: 50, max: 500 }),
            parking_spaces: faker.number.int({ min: 20, max: 200 }),
            accessibility: {
              wheelchair_accessible: faker.datatype.boolean({ probability: 0.9 }),
              elevator_access: faker.datatype.boolean({ probability: 0.8 }),
              hearing_loop: faker.datatype.boolean({ probability: 0.3 })
            }
          }
        });
      }
    }

    console.log(`✅ Generated ${this.data.facilities.length} facilities`);
  }

  private generateDepartments(): void {
    console.log('🏥 Generating Departments...');

    const departmentNames = [
      'Emergency Medicine', 'Internal Medicine', 'Cardiology', 'Orthopedics',
      'Pediatrics', 'Obstetrics & Gynecology', 'Radiology', 'Laboratory',
      'Pharmacy', 'Surgery', 'Neurology', 'Dermatology', 'Psychiatry',
      'Physical Therapy', 'Oncology', 'Endocrinology'
    ];

    for (const facility of this.data.facilities) {
      const deptCount = faker.number.int({ min: 2, max: CONFIG.DEPARTMENTS_PER_FACILITY });
      const selectedDepts = faker.helpers.arrayElements(departmentNames, deptCount);
      
      for (const deptName of selectedDepts) {
        this.data.departments.push({
          facility_id: `facility_${this.data.facilities.indexOf(facility)}`, // Will be replaced with actual UUID in SQL
          name: deptName,
          description: `${deptName} department providing specialized medical care`,
          metadata: {
            staff_count: faker.number.int({ min: 5, max: 25 }),
            operating_hours: {
              weekdays: { open: '07:00', close: '19:00' },
              weekends: { open: '08:00', close: '16:00' }
            },
            specialties: faker.helpers.arrayElements([
              'acute_care', 'chronic_care', 'preventive_care', 'emergency_care'
            ], faker.number.int({ min: 1, max: 3 }))
          }
        });
      }
    }

    console.log(`✅ Generated ${this.data.departments.length} departments`);
  }

  private generateHealthcareProviders(): void {
    console.log('👨‍⚕️ Generating Healthcare Providers...');

    const roles = ['physician', 'nurse', 'physician_assistant', 'nurse_practitioner', 'specialist'];
    const specialties = [
      'family_medicine', 'internal_medicine', 'cardiology', 'orthopedics',
      'pediatrics', 'emergency_medicine', 'radiology', 'surgery'
    ];

    for (const org of this.data.organizations) {
      const providerCount = faker.number.int({ min: 5, max: CONFIG.PROVIDERS_PER_ORG });
      
      for (let i = 0; i < providerCount; i++) {
        const firstName = faker.person.firstName();
        const lastName = faker.person.lastName();
        const role = faker.helpers.arrayElement(roles);
        
        this.data.healthcareProviders.push({
          organization_id: `org_${this.data.organizations.indexOf(org)}`,
          first_name: firstName,
          last_name: lastName,
          email: faker.internet.email({ firstName, lastName }),
          phone: faker.phone.number(),
          role: role as any, // Type assertion for enum
          specialty: faker.helpers.arrayElement(specialties) as any,
          license_number: faker.string.alphanumeric(10).toUpperCase(),
          credentials: {
            degrees: faker.helpers.arrayElements(['MD', 'DO', 'RN', 'NP', 'PA'], faker.number.int({ min: 1, max: 2 })),
            certifications: faker.helpers.arrayElements([
              'Board Certified Internal Medicine',
              'ACLS Certified',
              'BLS Certified',
              'Trauma Certified'
            ], faker.number.int({ min: 1, max: 3 })),
            years_experience: faker.number.int({ min: 1, max: 30 })
          }
        });
      }
    }

    console.log(`✅ Generated ${this.data.healthcareProviders.length} healthcare providers`);
  }
