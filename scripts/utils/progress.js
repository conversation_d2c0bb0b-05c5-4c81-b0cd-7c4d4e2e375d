// Progress tracking
let progress = {
  completed: [],
  current: '',
  remaining: []
};

export const updateProgress = (stage) => {
  if (progress.current) {
    progress.completed.push(progress.current);
  }
  progress.current = stage;
  console.log(`\n🔄 Starting: ${stage}`);
  console.log(`✅ Completed: ${progress.completed.length} stages`);
};

export const logProgress = (message) => {
  console.log(`   ${message}`);
};

export const getProgress = () => progress; 