import { DEPARTMENT_TYPES } from '#constants';
import { supabase } from '#database';
import { logProgress, randomElement, updateProgress } from '#helpers';
import { faker } from '@faker-js/faker';

// Tier 3: Departments & Teams
export async function generateTier3Departments() {
  console.log('🏢 Generating Tier 3: Departments & Teams...');
  
  try {
    // Fetch facilities from previous tier
    const { data: facilities, error: facilitiesError } = await supabase
      .from('facilities')
      .select('*');
    
    if (facilitiesError) throw facilitiesError;
    if (!facilities || facilities.length === 0) {
      throw new Error('No facilities found. Please run Tier 2 first.');
    }
    
    const results = {};
    
    // Departments
    results.departments = await seedDepartments(facilities);
    
    // Teams
    results.teams = await seedTeams(results.departments);
    
    console.log('✅ Tier 3 completed successfully');
    return results;
  } catch (error) {
    console.error('❌ Error in Tier 3:', error);
    throw error;
  }
}

async function seedDepartments(facilities) {
  updateProgress('Departments');
  
  const departments = [];
  
  for (const facility of facilities) {
    for (const deptType of DEPARTMENT_TYPES) {
      departments.push({
        facility_id: facility.id,
        name: generateDepartmentName(deptType, facility.name),
        type: deptType,
        settings: {
          description: generateDepartmentDescription(deptType),
          head_of_department: null, // Will be populated when providers are created
          operatingHours: generateDepartmentHours(deptType),
          capacity: generateDepartmentCapacity(deptType),
          specializations: generateDepartmentSpecializations(deptType),
          contact_info: {
            phone: faker.phone.number(),
            email: faker.internet.email(),
            extension: faker.number.int({ min: 1000, max: 9999 }).toString()
          },
          budget: {
            annual: faker.number.int({ min: 500000, max: 5000000 }),
            quarterly: faker.number.int({ min: 125000, max: 1250000 })
          }
        }
      });
    }
  }

  const { data, error } = await supabase.from('departments').insert(departments).select();
  if (error) throw error;
  
  logProgress(`Created ${departments.length} departments`);
  return data;
}

async function seedTeams(departments) {
  updateProgress('Teams');
  
  const teams = [];
  
  for (const dept of departments) {
    const teamCount = generateTeamCount(dept.type);
    
    for (let i = 0; i < teamCount; i++) {
      teams.push({
        department_id: dept.id,
        name: generateTeamName(dept.type, i),
        description: generateTeamDescription(dept.type),
        leader_id: null, // Will be populated when providers are created
        members: [] // Empty array for now, will be populated when providers are created
      });
    }
  }

  const { data, error } = await supabase.from('teams').insert(teams).select();
  if (error) throw error;
  
  logProgress(`Created ${teams.length} teams`);
  return data;
}

// Helper functions
function generateDepartmentName(type, facilityName) {
  const nameMap = {
    'primary_care': 'Primary Care',
    'pediatrics': 'Pediatrics',
    'cardiology': 'Cardiology',
    'neurology': 'Neurology',
    'orthopedics': 'Orthopedics',
    'emergency': 'Emergency Department',
    'laboratory': 'Laboratory Services',
    'pharmacy': 'Pharmacy',
    'radiology': 'Radiology',
    'billing': 'Billing & Revenue',
    'administration': 'Administration'
  };
  
  return `${facilityName} - ${nameMap[type]}`;
}

function generateDepartmentDescription(type) {
  const descriptions = {
    'primary_care': 'Comprehensive primary healthcare services for patients of all ages',
    'pediatrics': 'Specialized medical care for infants, children, and adolescents',
    'cardiology': 'Diagnosis and treatment of heart and cardiovascular conditions',
    'neurology': 'Treatment of disorders affecting the nervous system',
    'orthopedics': 'Musculoskeletal system care including bones, joints, and muscles',
    'emergency': '24/7 emergency medical care and trauma services',
    'laboratory': 'Diagnostic testing and pathology services',
    'pharmacy': 'Medication dispensing and pharmaceutical care',
    'radiology': 'Medical imaging and diagnostic radiology services',
    'billing': 'Revenue cycle management and insurance processing',
    'administration': 'Administrative support and facility management'
  };
  
  return descriptions[type];
}

function generateDepartmentHours(type) {
  if (type === 'emergency') {
    return { start: '00:00', end: '23:59', days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'] };
  }
  
  if (type === 'laboratory' || type === 'pharmacy') {
    return { start: '06:00', end: '22:00', days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'] };
  }
  
  return { start: '08:00', end: '17:00', days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'] };
}

function generateDepartmentCapacity(type) {
  const capacityMap = {
    'primary_care': { beds: 0, rooms: faker.number.int({ min: 10, max: 20 }), staff: faker.number.int({ min: 15, max: 30 }) },
    'pediatrics': { beds: faker.number.int({ min: 5, max: 15 }), rooms: faker.number.int({ min: 8, max: 15 }), staff: faker.number.int({ min: 10, max: 25 }) },
    'cardiology': { beds: faker.number.int({ min: 10, max: 25 }), rooms: faker.number.int({ min: 6, max: 12 }), staff: faker.number.int({ min: 12, max: 20 }) },
    'emergency': { beds: faker.number.int({ min: 20, max: 40 }), rooms: faker.number.int({ min: 15, max: 25 }), staff: faker.number.int({ min: 25, max: 50 }) },
    'laboratory': { beds: 0, rooms: faker.number.int({ min: 5, max: 10 }), staff: faker.number.int({ min: 8, max: 15 }) }
  };
  
  return capacityMap[type] || { beds: faker.number.int({ min: 5, max: 15 }), rooms: faker.number.int({ min: 5, max: 10 }), staff: faker.number.int({ min: 8, max: 15 }) };
}

function generateDepartmentSpecializations(type) {
  const specializationMap = {
    'cardiology': ['Interventional Cardiology', 'Electrophysiology', 'Heart Failure', 'Preventive Cardiology'],
    'neurology': ['Stroke Care', 'Epilepsy', 'Movement Disorders', 'Headache Medicine'],
    'orthopedics': ['Joint Replacement', 'Sports Medicine', 'Spine Surgery', 'Trauma'],
    'pediatrics': ['Neonatology', 'Pediatric Cardiology', 'Developmental Pediatrics'],
    'emergency': ['Trauma', 'Critical Care', 'Toxicology', 'Emergency Surgery']
  };
  
  return specializationMap[type] || [];
}

function generateTeamCount(deptType) {
  const countMap = {
    'emergency': faker.number.int({ min: 3, max: 5 }),
    'primary_care': faker.number.int({ min: 2, max: 4 }),
    'cardiology': faker.number.int({ min: 2, max: 3 }),
    'laboratory': faker.number.int({ min: 2, max: 3 })
  };
  
  return countMap[deptType] || faker.number.int({ min: 1, max: 3 });
}

function generateTeamName(deptType, index) {
  const teamNames = {
    'emergency': ['Trauma Team', 'Triage Team', 'Critical Care Team', 'Rapid Response Team'],
    'primary_care': ['Family Medicine Team', 'Wellness Team', 'Chronic Care Team'],
    'cardiology': ['Interventional Team', 'Diagnostic Team', 'Heart Failure Team'],
    'laboratory': ['Hematology Team', 'Chemistry Team', 'Microbiology Team'],
    'pharmacy': ['Clinical Pharmacy Team', 'Dispensing Team'],
    'radiology': ['CT Team', 'MRI Team', 'Ultrasound Team']
  };
  
  const names = teamNames[deptType] || ['Alpha Team', 'Beta Team', 'Gamma Team'];
  return names[index] || `Team ${index + 1}`;
}

function generateTeamType(deptType) {
  const typeMap = {
    'emergency': 'clinical',
    'primary_care': 'clinical',
    'cardiology': 'clinical',
    'laboratory': 'diagnostic',
    'pharmacy': 'support',
    'billing': 'administrative',
    'administration': 'administrative'
  };
  
  return typeMap[deptType] || 'clinical';
}

function generateTeamDescription(deptType) {
  return `Specialized team providing ${deptType} services with focus on patient care and operational excellence`;
}

function generateShiftPattern(deptType) {
  if (deptType === 'emergency') {
    return randomElement(['12-hour', '8-hour rotating', '24-hour on-call']);
  }
  
  return randomElement(['8-hour', 'day shift', 'standard business hours']);
}

function generateRequiredCertifications(deptType) {
  const certMap = {
    'emergency': ['BLS', 'ACLS', 'PALS', 'ATLS'],
    'cardiology': ['BLS', 'ACLS', 'Cardiac Catheterization'],
    'pediatrics': ['BLS', 'PALS', 'Pediatric Advanced Care'],
    'laboratory': ['Medical Laboratory Scientist', 'Phlebotomy'],
    'pharmacy': ['PharmD', 'Board Certification']
  };
  
  return certMap[deptType] || ['BLS'];
}

function generateTeamSpecialization(deptType) {
  const specializations = generateDepartmentSpecializations(deptType);
  return specializations.length > 0 ? randomElement(specializations) : null;
} 