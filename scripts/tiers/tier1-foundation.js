import { supabase } from '#database';
import { logProgress, randomElement, updateProgress } from '#helpers';
import { faker } from '@faker-js/faker';

// Tier 1: Foundation
export async function generateTier1Foundation() {
  console.log('🏗️ Generating Tier 1: Foundation...');
  
  try {
    const results = {};
    
    // Role Definitions
    results.roleDefinitions = await seedRoleDefinitions();
    
    // Insurance Providers  
    results.insuranceProviders = await seedInsuranceProviders();
    
    // Education Materials
    results.educationMaterials = await seedEducationMaterials();
    
    updateProgress('tier1', 'completed');
    console.log('✅ Tier 1: Foundation completed successfully!');
    
    return results;
  } catch (error) {
    console.error('❌ Error in Tier 1:', error);
    throw error;
  }
}

async function seedRoleDefinitions() {
  console.log('Creating role definitions...');
  
  const roles = [
    {
      role: 'system_admin',
      description: 'System administrator with full access',
      base_permissions: { all: '*' }
    },
    {
      role: 'org_admin',
      description: 'Organization administrator',
      base_permissions: { organization: 'manage', users: 'manage', billing: 'view' }
    },
    {
      role: 'clinical_admin',
      description: 'Clinical administrator',
      base_permissions: { clinical: 'manage', staff: 'manage', schedules: 'manage' }
    },
    {
      role: 'physician',
      description: 'Licensed physician',
      base_permissions: { patients: 'full', prescriptions: 'write', orders: 'create' }
    },
    {
      role: 'nurse_practitioner',
      description: 'Nurse practitioner',
      base_permissions: { patients: 'full', prescriptions: 'limited', orders: 'create' }
    },
    {
      role: 'registered_nurse',
      description: 'Registered nurse',
      base_permissions: { patients: 'care', vitals: 'record', medications: 'administer' }
    },
    {
      role: 'medical_assistant',
      description: 'Medical assistant',
      base_permissions: { patients: 'basic', appointments: 'schedule', vitals: 'record' }
    },
    {
      role: 'front_desk',
      description: 'Front desk staff',
      base_permissions: { appointments: 'manage', patients: 'demographic', billing: 'basic' }
    },
    {
      role: 'billing_staff',
      description: 'Billing and coding staff',
      base_permissions: { billing: 'full', claims: 'process', payments: 'manage' }
    },
    {
      role: 'pharmacist',
      description: 'Licensed pharmacist',
      base_permissions: { medications: 'dispense', prescriptions: 'review', inventory: 'manage' }
    },
    {
      role: 'lab_technician',
      description: 'Laboratory technician',
      base_permissions: { lab: 'full', results: 'enter', specimens: 'process' }
    },
    {
      role: 'patient',
      description: 'Patient user',
      base_permissions: { portal: 'access', appointments: 'view', records: 'own' }
    }
  ];

  // Use upsert to handle existing roles gracefully
  const { data, error } = await supabase
    .from('role_definitions')
    .upsert(roles, { 
      onConflict: 'role',
      ignoreDuplicates: false 
    })
    .select();
    
  if (error) throw error;
  
  logProgress(`✅ Created/updated ${roles.length} role definitions`);
  return data || roles;
}

async function seedInsuranceProviders() {
  console.log('Creating insurance providers...');
  
  const providers = [
    'Blue Cross Blue Shield', 'Aetna', 'Cigna', 'UnitedHealthcare', 'Humana',
    'Kaiser Permanente', 'Anthem', 'Molina Healthcare', 'Centene', 'Medicare',
    'Medicaid', 'Tricare', 'GEHA', 'Independence Blue Cross', 'Highmark'
  ].map(name => ({
    name,
    contact_info: {
      phone: faker.phone.number(),
      email: faker.internet.email(),
      website: faker.internet.url(),
      address: {
        street: faker.location.streetAddress(),
        city: faker.location.city(),
        state: faker.location.state(),
        zipCode: faker.location.zipCode()
      }
    },
    settings: {
      claimSubmissionMethod: randomElement(['electronic', 'paper', 'both']),
      priorAuthRequired: faker.datatype.boolean(),
      copayRequired: faker.datatype.boolean()
    }
  }));

  // Check which providers already exist
  const { data: existingProviders } = await supabase
    .from('insurance_providers')
    .select('name');
  
  const existingNames = new Set(existingProviders?.map(p => p.name) || []);
  const newProviders = providers.filter(p => !existingNames.has(p.name));
  
  let data = [];
  if (newProviders.length > 0) {
    const { data: insertedData, error } = await supabase
      .from('insurance_providers')
      .insert(newProviders)
      .select();
    if (error) throw error;
    data = insertedData;
  }
  
  logProgress(`✅ Created ${newProviders.length} new insurance providers (${existingNames.size} already existed)`);
  return data;
}

async function seedEducationMaterials() {
  console.log('Creating education materials...');
  
  const materials = [
    {
      title: 'Managing Diabetes',
      category: 'Diabetes Care',
      content: 'Comprehensive guide to managing Type 2 diabetes through diet, exercise, and medication.',
      format: 'text',
      language: 'en'
    },
    {
      title: 'Heart Healthy Living',
      category: 'Cardiovascular Health',
      content: 'Tips for maintaining cardiovascular health through lifestyle changes.',
      format: 'pdf',
      language: 'en'
    },
    {
      title: 'Vaccination Schedule',
      category: 'Preventive Care',
      content: 'Complete vaccination schedule for children and adults.',
      format: 'interactive',
      language: 'en'
    },
    {
      title: 'Pre-Surgery Instructions',
      category: 'Surgical Care',
      content: 'Important instructions to follow before surgical procedures.',
      format: 'text',
      language: 'en'
    }
  ].map(material => ({
    ...material,
    metadata: {
      lastReviewed: faker.date.recent({ days: 30 }).toISOString(),
      reviewedBy: faker.person.fullName(),
      version: '1.0'
    }
  }));

  // Check which materials already exist
  const { data: existingMaterials } = await supabase
    .from('education_materials')
    .select('title');
  
  const existingTitles = new Set(existingMaterials?.map(m => m.title) || []);
  const newMaterials = materials.filter(m => !existingTitles.has(m.title));
  
  let data = [];
  if (newMaterials.length > 0) {
    const { data: insertedData, error } = await supabase
      .from('education_materials')
      .insert(newMaterials)
      .select();
    if (error) throw error;
    data = insertedData;
  }
  
  logProgress(`✅ Created ${newMaterials.length} new education materials (${existingTitles.size} already existed)`);
  return data;
} 