import { supabase } from '#database';
import { getRandomElement, logProgress, updateProgress } from '#helpers';
import { faker } from '@faker-js/faker';

export async function generateTier12AnalyticsAudit() {
  console.log('📊 Generating Tier 12: Analytics & Audit...');
  
  try {
    const { data: organizations } = await supabase.from('organizations').select('id');
    const { data: providers } = await supabase.from('healthcare_providers').select('id, organization_id');

    // Generate Analytics Events
    console.log('Creating analytics events...');
    const analyticsEvents = [];
    const eventTypes = [
      'user_login', 'appointment_scheduled', 'medical_record_created',
      'prescription_created', 'lab_result_viewed', 'patient_search'
    ];
    
    for (let i = 0; i < 1000; i++) {
      const organization = getRandomElement(organizations);
      const user = getRandomElement(providers.filter(p => p.organization_id === organization.id));
      
      analyticsEvents.push({
        event_type: getRandomElement(eventTypes),
        user_id: user?.id,
        organization_id: organization.id,
        timestamp: faker.date.recent({ days: 30 }).toISOString(),
        event_data: {
          session_id: faker.string.uuid(),
          duration: faker.number.int({ min: 1, max: 1800 }),
          success: faker.datatype.boolean({ probability: 0.95 })
        }
      });
    }
    
    if (analyticsEvents.length > 0) {
      const { error } = await supabase.from('analytics_events').insert(analyticsEvents);
      if (error) throw error;
      logProgress(`✅ Created ${analyticsEvents.length} analytics events`);
    }

    // Generate Analytics Metrics
    console.log('Creating analytics metrics...');
    const analyticsMetrics = [];
    const metricNames = [
      'daily_active_users', 'appointments_per_day', 'patient_registrations',
      'prescription_count', 'lab_results_processed', 'error_rate'
    ];
    
    for (const organization of organizations) {
      for (let day = 0; day < 30; day++) {
        const date = new Date();
        date.setDate(date.getDate() - day);
        
        for (const metricName of metricNames) {
          analyticsMetrics.push({
            organization_id: organization.id,
            metric_name: metricName,
            metric_value: faker.number.int({ min: 1, max: 500 }),
            timestamp: date.toISOString(),
            dimensions: {
              date: date.toISOString().split('T')[0],
              organization_id: organization.id
            }
          });
        }
      }
    }
    
    if (analyticsMetrics.length > 0) {
      const { error } = await supabase.from('analytics_metrics').insert(analyticsMetrics);
      if (error) throw error;
      logProgress(`✅ Created ${analyticsMetrics.length} analytics metrics`);
    }

    // Generate Audit Logs
    console.log('Creating audit logs...');
    const auditLogs = [];
    const tableNames = ['patients', 'healthcare_providers', 'medical_records', 'appointments'];
    const actions = ['INSERT', 'UPDATE', 'DELETE'];
    
    for (let i = 0; i < 800; i++) {
      auditLogs.push({
        table_name: getRandomElement(tableNames),
        action: getRandomElement(actions),
        record_id: faker.string.uuid(),
        old_data: { field: 'old_value' },
        new_data: { field: 'new_value' },
        changed_by: getRandomElement(providers)?.id,
        timestamp: faker.date.recent({ days: 30 }).toISOString()
      });
    }
    
    if (auditLogs.length > 0) {
      const { error } = await supabase.from('audit_logs').insert(auditLogs);
      if (error) throw error;
      logProgress(`✅ Created ${auditLogs.length} audit logs`);
    }

    // Generate Activity Logs
    console.log('Creating activity logs...');
    const activityLogs = [];
    const actionTypes = [
      'view_patient', 'edit_patient', 'create_appointment', 'prescribe_medication',
      'order_lab_test', 'send_message', 'login', 'logout'
    ];
    
    for (let i = 0; i < 1200; i++) {
      const organization = getRandomElement(organizations);
      
      activityLogs.push({
        organization_id: organization.id,
        user_id: getRandomElement(providers.filter(p => p.organization_id === organization.id))?.id,
        action_type: getRandomElement(actionTypes),
        resource_type: getRandomElement(['patient', 'appointment', 'medical_record']),
        resource_id: faker.string.uuid(),
        details: {
          action: getRandomElement(actionTypes),
          duration_ms: faker.number.int({ min: 100, max: 5000 }),
          success: faker.datatype.boolean({ probability: 0.95 })
        }
      });
    }
    
    if (activityLogs.length > 0) {
      const { error } = await supabase.from('activity_logs').insert(activityLogs);
      if (error) throw error;
      logProgress(`✅ Created ${activityLogs.length} activity logs`);
    }

    updateProgress('tier12', 'completed');
    console.log('✅ Tier 12: Analytics & Audit completed successfully!');
    
  } catch (error) {
    console.error('❌ Error in Tier 12:', error);
    throw error;
  }
} 