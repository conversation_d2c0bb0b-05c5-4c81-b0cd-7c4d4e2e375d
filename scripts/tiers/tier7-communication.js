import { supabase } from '#database';
import { logProgress, randomElement, updateProgress } from '#helpers';
import { faker } from '@faker-js/faker';

// Tier 7: Communication & Documentation
export async function generateTier7Communication() {
  console.log('💬 Generating Tier 7: Communication & Documentation...');
  
  try {
    // Fetch required data from previous tiers
    const { data: organizations, error: orgsError } = await supabase
      .from('organizations')
      .select('*');
    
    if (orgsError) throw orgsError;
    if (!organizations || organizations.length === 0) {
      throw new Error('No organizations found. Please run Tier 2 first.');
    }
    
    const { data: patients, error: patientsError } = await supabase
      .from('patients')
      .select('*');
    
    if (patientsError) throw patientsError;
    if (!patients || patients.length === 0) {
      throw new Error('No patients found. Please run Tier 4 first.');
    }
    
    const { data: healthcareProviders, error: providersError } = await supabase
      .from('healthcare_providers')
      .select('*');
    
    if (providersError) throw providersError;
    if (!healthcareProviders || healthcareProviders.length === 0) {
      throw new Error('No healthcare providers found. Please run Tier 4 first.');
    }
    
    const { data: medicalRecords, error: recordsError } = await supabase
      .from('medical_records')
      .select('*');
    
    if (recordsError) throw recordsError;
    if (!medicalRecords || medicalRecords.length === 0) {
      throw new Error('No medical records found. Please run Tier 5 first.');
    }
    
    const { data: appointments, error: appointmentsError } = await supabase
      .from('appointments')
      .select('*');
    
    if (appointmentsError) throw appointmentsError;
    if (!appointments || appointments.length === 0) {
      throw new Error('No appointments found. Please run Tier 5 first.');
    }
    
    const { data: labResults, error: labError } = await supabase
      .from('lab_results')
      .select('*');
    
    if (labError) throw labError;
    if (!labResults || labResults.length === 0) {
      throw new Error('No lab results found. Please run Tier 5 first.');
    }
    
    const { data: prescriptions, error: prescriptionsError } = await supabase
      .from('prescriptions')
      .select('*');
    
    if (prescriptionsError) throw prescriptionsError;
    if (!prescriptions || prescriptions.length === 0) {
      throw new Error('No prescriptions found. Please run Tier 5 first.');
    }
    
    const results = {};
    
    // Clinical Notes
    results.clinicalNotes = await seedClinicalNotes(patients, healthcareProviders, medicalRecords, appointments);
    
    // Messages
    results.messages = await seedMessages(organizations, patients, healthcareProviders);
    
    // Notifications
    results.notifications = await seedNotifications(patients, healthcareProviders, appointments, labResults, prescriptions);
    
    // Documents
    results.documents = await seedDocuments(patients, healthcareProviders, medicalRecords);
    
    console.log('✅ Tier 7 completed successfully');
    return results;
  } catch (error) {
    console.error('❌ Error in Tier 7:', error);
    throw error;
  }
}

async function seedClinicalNotes(patients, healthcareProviders, medicalRecords, appointments) {
  updateProgress('Clinical Notes');
  
  const clinicalNotes = [];
  
  // Create clinical notes for medical records and some appointments
  for (const record of medicalRecords) {
    const noteCount = faker.number.int({ min: 1, max: 3 });
    
    for (let i = 0; i < noteCount; i++) {
      const provider = healthcareProviders.find(p => p.id === record.provider_id);
      const patient = patients.find(p => p.id === record.patient_id);
      
      clinicalNotes.push({
        patient_id: record.patient_id,
        provider_id: record.provider_id,
        medical_record_id: record.id,
        appointment_id: record.appointment_id,
        organization_id: record.organization_id,
        note_type: generateNoteType(provider.specialty),
        note_date: record.encounter_date,
        note_time: faker.date.recent({ days: 1 }).toTimeString().split(' ')[0].substring(0, 5),
        title: generateNoteTitle(provider.specialty),
        content: generateNoteContent(provider.specialty, patient),
        template_used: generateTemplate(provider.specialty),
        status: randomElement(['draft', 'signed', 'amended', 'final']),
        created_by: provider.id,
        last_modified: faker.date.recent({ days: 7 }).toISOString(),
        tags: generateNoteTags(provider.specialty),
        priority: randomElement(['routine', 'urgent', 'stat']),
        confidentiality: randomElement(['normal', 'restricted', 'confidential']),
        addendum: faker.datatype.boolean({ probability: 0.1 }) ? generateAddendum() : null
      });
    }
  }

  const { data, error } = await supabase.from('clinical_notes').insert(clinicalNotes).select();
  if (error) throw error;
  
  logProgress(`Created ${clinicalNotes.length} clinical notes`);
  return data;
}

async function seedMessages(organizations, patients, healthcareProviders) {
  updateProgress('Messages');
  
  const messages = [];
  
  for (const org of organizations) {
    const orgProviders = healthcareProviders.filter(p => p.organization_id === org.id);
    const orgPatients = patients.filter(p => p.organization_id === org.id);
    const messageCount = faker.number.int({ min: 50, max: 200 });
    
    for (let i = 0; i < messageCount; i++) {
      const messageType = randomElement(['provider_to_provider', 'provider_to_patient', 'patient_to_provider', 'system_alert']);
      
      let sender, recipient, subject, content;
      
      switch (messageType) {
        case 'provider_to_provider':
          sender = randomElement(orgProviders);
          recipient = randomElement(orgProviders.filter(p => p.id !== sender.id));
          subject = generateProviderSubject();
          content = generateProviderContent();
          break;
          
        case 'provider_to_patient':
          sender = randomElement(orgProviders);
          recipient = randomElement(orgPatients);
          subject = generatePatientSubject();
          content = generatePatientContent();
          break;
          
        case 'patient_to_provider':
          sender = randomElement(orgPatients);
          recipient = randomElement(orgProviders);
          subject = generatePatientInquirySubject();
          content = generatePatientInquiryContent();
          break;
          
        case 'system_alert':
          sender = null;
          recipient = randomElement(orgProviders);
          subject = generateSystemAlertSubject();
          content = generateSystemAlertContent();
          break;
      }
      
      messages.push({
        organization_id: org.id,
        sender_type: sender ? (orgProviders.includes(sender) ? 'provider' : 'patient') : 'system',
        sender_id: sender?.id || null,
        recipient_type: orgProviders.includes(recipient) ? 'provider' : 'patient',
        recipient_id: recipient.id,
        message_type: messageType,
        subject: subject,
        content: content,
        priority: randomElement(['low', 'normal', 'high', 'urgent']),
        status: randomElement(['sent', 'delivered', 'read', 'archived']),
        sent_date: faker.date.past({ days: 30 }).toISOString(),
        read_date: faker.datatype.boolean({ probability: 0.8 }) ? faker.date.recent({ days: 20 }).toISOString() : null,
        thread_id: faker.datatype.boolean({ probability: 0.3 }) ? faker.string.uuid() : null,
        attachments: generateAttachments(),
        encryption_status: randomElement(['encrypted', 'not_encrypted']),
        delivery_method: randomElement(['portal', 'email', 'sms', 'in_app']),
        auto_generated: messageType === 'system_alert',
        expires_date: messageType === 'system_alert' ? faker.date.future({ days: 30 }).toISOString() : null
      });
    }
  }

  const { data, error } = await supabase.from('messages').insert(messages).select();
  if (error) throw error;
  
  logProgress(`Created ${messages.length} messages`);
  return data;
}

async function seedNotifications(patients, healthcareProviders, appointments, labResults, prescriptions) {
  updateProgress('Notifications');
  
  const notifications = [];
  
  // Appointment reminders
  const upcomingAppointments = appointments.filter(a => new Date(a.appointment_date) > new Date());
  for (const appointment of upcomingAppointments) {
    const patient = patients.find(p => p.id === appointment.patient_id);
    
    if (patient.preferences.appointment_reminders) {
      notifications.push({
        recipient_type: 'patient',
        recipient_id: appointment.patient_id,
        notification_type: 'appointment_reminder',
        title: 'Upcoming Appointment Reminder',
        message: `You have an appointment scheduled for ${appointment.appointment_date} at ${appointment.appointment_time}`,
        priority: 'normal',
        status: 'pending',
        scheduled_date: subtractDays(appointment.appointment_date, 1),
        sent_date: null,
        read_date: null,
        delivery_method: patient.preferences.communication_method,
        metadata: {
          appointment_id: appointment.id,
          provider_name: `Dr. ${faker.person.lastName()}`,
          appointment_type: appointment.type
        },
        expires_date: appointment.appointment_date,
        auto_retry: true,
        max_retries: 3
      });
    }
  }
  
  // Lab result notifications
  for (const labResult of labResults) {
    const patient = patients.find(p => p.id === labResult.patient_id);
    const provider = healthcareProviders.find(p => p.id === labResult.provider_id);
    
    // Notify patient
    notifications.push({
      recipient_type: 'patient',
      recipient_id: labResult.patient_id,
      notification_type: 'lab_results_ready',
      title: 'Lab Results Available',
      message: `Your ${labResult.test_name} results are now available in your patient portal`,
      priority: Object.keys(labResult.abnormal_flags).length > 0 ? 'high' : 'normal',
      status: 'sent',
      scheduled_date: labResult.completed_date,
      sent_date: labResult.completed_date,
      read_date: faker.datatype.boolean({ probability: 0.6 }) ? faker.date.recent({ days: 5 }).toISOString() : null,
      delivery_method: patient.preferences.communication_method,
      metadata: {
        lab_result_id: labResult.id,
        test_name: labResult.test_name,
        has_abnormal_values: Object.keys(labResult.abnormal_flags).length > 0
      },
      expires_date: addDays(labResult.completed_date, 30),
      auto_retry: false,
      max_retries: 1
    });
    
    // Notify provider if abnormal
    if (Object.keys(labResult.abnormal_flags).length > 0) {
      notifications.push({
        recipient_type: 'provider',
        recipient_id: labResult.provider_id,
        notification_type: 'abnormal_lab_alert',
        title: 'Abnormal Lab Results',
        message: `Patient ${patient.first_name} ${patient.last_name} has abnormal ${labResult.test_name} results requiring review`,
        priority: 'high',
        status: 'sent',
        scheduled_date: labResult.completed_date,
        sent_date: labResult.completed_date,
        read_date: faker.datatype.boolean({ probability: 0.9 }) ? faker.date.recent({ days: 2 }).toISOString() : null,
        delivery_method: 'in_app',
        metadata: {
          lab_result_id: labResult.id,
          patient_id: labResult.patient_id,
          abnormal_flags: labResult.abnormal_flags
        },
        expires_date: addDays(labResult.completed_date, 7),
        auto_retry: true,
        max_retries: 5
      });
    }
  }
  
  // Prescription notifications
  for (const prescription of prescriptions) {
    const patient = patients.find(p => p.id === prescription.patient_id);
    
    // Prescription ready notification
    if (prescription.status === 'active') {
      notifications.push({
        recipient_type: 'patient',
        recipient_id: prescription.patient_id,
        notification_type: 'prescription_ready',
        title: 'Prescription Ready for Pickup',
        message: `Your prescription for ${prescription.medication_name} is ready for pickup at ${prescription.pharmacy_info.pharmacy_name}`,
        priority: 'normal',
        status: 'sent',
        scheduled_date: addDays(prescription.prescribed_date, 1),
        sent_date: addDays(prescription.prescribed_date, 1),
        read_date: faker.datatype.boolean({ probability: 0.7 }) ? faker.date.recent({ days: 3 }).toISOString() : null,
        delivery_method: patient.preferences.communication_method,
        metadata: {
          prescription_id: prescription.id,
          medication_name: prescription.medication_name,
          pharmacy_name: prescription.pharmacy_info.pharmacy_name
        },
        expires_date: addDays(prescription.prescribed_date, 14),
        auto_retry: false,
        max_retries: 2
      });
    }
    
    // Refill reminder (for prescriptions nearing end date)
    if (prescription.refills > 0 && new Date(prescription.end_date) > new Date()) {
      notifications.push({
        recipient_type: 'patient',
        recipient_id: prescription.patient_id,
        notification_type: 'prescription_refill_reminder',
        title: 'Prescription Refill Reminder',
        message: `Your prescription for ${prescription.medication_name} is running low. You have ${prescription.refills} refills remaining.`,
        priority: 'low',
        status: 'pending',
        scheduled_date: subtractDays(prescription.end_date, 7),
        sent_date: null,
        read_date: null,
        delivery_method: patient.preferences.communication_method,
        metadata: {
          prescription_id: prescription.id,
          medication_name: prescription.medication_name,
          refills_remaining: prescription.refills
        },
        expires_date: prescription.end_date,
        auto_retry: true,
        max_retries: 2
      });
    }
  }

  const { data, error } = await supabase.from('notifications').insert(notifications).select();
  if (error) throw error;
  
  logProgress(`Created ${notifications.length} notifications`);
  return data;
}

async function seedDocuments(patients, healthcareProviders, medicalRecords) {
  updateProgress('Documents');
  
  const documents = [];
  
  // Medical documents for each patient
  for (const patient of patients) {
    const documentCount = faker.number.int({ min: 2, max: 8 });
    
    for (let i = 0; i < documentCount; i++) {
      const docType = generateDocumentType();
      const provider = randomElement(healthcareProviders.filter(p => p.organization_id === patient.organization_id));
      
      documents.push({
        patient_id: patient.id,
        organization_id: patient.organization_id,
        uploaded_by_type: randomElement(['provider', 'patient', 'admin']),
        uploaded_by_id: provider.id,
        document_type: docType,
        document_category: generateDocumentCategory(docType),
        title: generateDocumentTitle(docType),
        description: generateDocumentDescription(docType),
        file_name: generateFileName(docType),
        file_size: faker.number.int({ min: 100000, max: 5000000 }), // bytes
        file_type: generateFileType(docType),
        file_path: generateFilePath(docType),
        upload_date: faker.date.past({ years: 2 }).toISOString(),
        document_date: faker.date.past({ years: 3 }).toISOString().split('T')[0],
        status: randomElement(['active', 'archived', 'deleted']),
        access_level: randomElement(['patient', 'provider', 'organization', 'restricted']),
        version: '1.0',
        is_signed: faker.datatype.boolean({ probability: 0.7 }),
        signature_date: faker.datatype.boolean({ probability: 0.7 }) ? faker.date.recent({ days: 30 }).toISOString() : null,
        retention_date: generateRetentionDate(),
        tags: generateDocumentTags(docType),
        metadata: generateDocumentMetadata(docType),
        encryption_status: randomElement(['encrypted', 'not_encrypted']),
        hipaa_compliant: true,
        audit_trail: generateAuditTrail()
      });
    }
  }

  const { data, error } = await supabase.from('documents').insert(documents).select();
  if (error) throw error;
  
  logProgress(`Created ${documents.length} documents`);
  return data;
}

// Helper functions
function generateNoteType(specialty) {
  const types = {
    'Family Medicine': ['Progress Note', 'History & Physical', 'Consultation Note'],
    'Pediatrics': ['Well Child Note', 'Sick Visit Note', 'Developmental Assessment'],
    'Cardiology': ['Cardiology Consultation', 'Echo Report', 'Cardiac Catheterization Note'],
    'Emergency Medicine': ['Emergency Department Note', 'Trauma Note', 'Discharge Summary'],
    'default': ['Progress Note', 'Consultation Note', 'Procedure Note']
  };
  
  const typeList = types[specialty] || types.default;
  return randomElement(typeList);
}

function generateNoteTitle(specialty) {
  const titles = [
    'Follow-up visit', 'Initial consultation', 'Post-procedure check',
    'Medication review', 'Symptom assessment', 'Treatment plan update'
  ];
  
  return randomElement(titles);
}

function generateNoteContent(specialty, patient) {
  const templates = [
    `Patient ${patient.first_name} presents for follow-up. Reports feeling well overall. No acute concerns noted during today's visit.`,
    `Reviewed current medications and treatment plan. Patient demonstrates good understanding of condition and compliance with therapy.`,
    `Physical examination reveals stable findings. Plan to continue current treatment approach with follow-up in 3 months.`,
    `Patient reports improvement in symptoms since last visit. Will monitor closely and adjust treatment as needed.`
  ];
  
  return randomElement(templates);
}

function generateTemplate(specialty) {
  const templates = {
    'Family Medicine': ['Primary Care Note Template', 'Annual Physical Template'],
    'Pediatrics': ['Pediatric Visit Template', 'Well Child Template'],
    'Cardiology': ['Cardiology Consultation Template', 'Cardiac Procedure Template'],
    'default': ['Standard Progress Note Template', 'Consultation Template']
  };
  
  const templateList = templates[specialty] || templates.default;
  return randomElement(templateList);
}

function generateNoteTags(specialty) {
  const tags = ['routine', 'follow-up', 'new-patient', 'urgent', 'medication-review', 'procedure'];
  return faker.helpers.arrayElements(tags, faker.number.int({ min: 1, max: 3 }));
}

function generateAddendum() {
  return {
    content: faker.lorem.sentences(2),
    added_by: faker.person.fullName(),
    added_date: faker.date.recent({ days: 5 }).toISOString(),
    reason: 'Additional information'
  };
}

function generateProviderSubject() {
  const subjects = [
    'Patient consultation request', 'Lab results review', 'Treatment plan discussion',
    'Case conference follow-up', 'Medication interaction alert', 'Referral coordination'
  ];
  return randomElement(subjects);
}

function generateProviderContent() {
  return faker.lorem.sentences(faker.number.int({ min: 2, max: 5 }));
}

function generatePatientSubject() {
  const subjects = [
    'Test results available', 'Appointment confirmation', 'Prescription update',
    'Follow-up instructions', 'Care plan update', 'Health education material'
  ];
  return randomElement(subjects);
}

function generatePatientContent() {
  return faker.lorem.sentences(faker.number.int({ min: 1, max: 3 }));
}

function generatePatientInquirySubject() {
  const subjects = [
    'Question about medication', 'Appointment scheduling request', 'Symptom concern',
    'Insurance coverage question', 'Test result inquiry', 'Prescription refill request'
  ];
  return randomElement(subjects);
}

function generatePatientInquiryContent() {
  return faker.lorem.sentences(faker.number.int({ min: 1, max: 4 }));
}

function generateSystemAlertSubject() {
  const subjects = [
    'Critical lab value alert', 'Medication interaction warning', 'Appointment reminder system',
    'Insurance authorization required', 'Patient portal access issue', 'System maintenance notification'
  ];
  return randomElement(subjects);
}

function generateSystemAlertContent() {
  return faker.lorem.sentences(faker.number.int({ min: 1, max: 2 }));
}

function generateAttachments() {
  if (faker.datatype.boolean({ probability: 0.3 })) {
    return [
      {
        filename: faker.system.fileName(),
        size: faker.number.int({ min: 1000, max: 500000 }),
        type: randomElement(['pdf', 'jpg', 'png', 'doc'])
      }
    ];
  }
  return [];
}

function generateDocumentType() {
  const types = [
    'medical_record', 'lab_report', 'imaging_study', 'consent_form',
    'insurance_card', 'identification', 'referral_letter', 'discharge_summary',
    'prescription', 'vaccination_record', 'allergy_list', 'medication_list'
  ];
  return randomElement(types);
}

function generateDocumentCategory(docType) {
  const categoryMap = {
    'medical_record': 'Clinical',
    'lab_report': 'Diagnostic',
    'imaging_study': 'Diagnostic',
    'consent_form': 'Administrative',
    'insurance_card': 'Insurance',
    'identification': 'Personal',
    'referral_letter': 'Clinical',
    'discharge_summary': 'Clinical',
    'prescription': 'Medication',
    'vaccination_record': 'Preventive',
    'allergy_list': 'Clinical',
    'medication_list': 'Medication'
  };
  
  return categoryMap[docType] || 'General';
}

function generateDocumentTitle(docType) {
  const titleMap = {
    'medical_record': 'Medical Record',
    'lab_report': 'Laboratory Report',
    'imaging_study': 'Imaging Study Report',
    'consent_form': 'Informed Consent Form',
    'insurance_card': 'Insurance Card Copy',
    'identification': 'Government ID',
    'referral_letter': 'Specialist Referral',
    'discharge_summary': 'Hospital Discharge Summary',
    'prescription': 'Prescription Record',
    'vaccination_record': 'Vaccination History',
    'allergy_list': 'Known Allergies',
    'medication_list': 'Current Medications'
  };
  
  return titleMap[docType] || 'Medical Document';
}

function generateDocumentDescription(docType) {
  return `${generateDocumentTitle(docType)} - ${faker.lorem.sentence()}`;
}

function generateFileName(docType) {
  const extensions = {
    'medical_record': 'pdf',
    'lab_report': 'pdf',
    'imaging_study': 'jpg',
    'consent_form': 'pdf',
    'insurance_card': 'jpg',
    'identification': 'jpg'
  };
  
  const ext = extensions[docType] || 'pdf';
  return `${docType}_${faker.string.alphanumeric(8)}.${ext}`;
}

function generateFileType(docType) {
  const typeMap = {
    'medical_record': 'application/pdf',
    'lab_report': 'application/pdf',
    'imaging_study': 'image/jpeg',
    'consent_form': 'application/pdf',
    'insurance_card': 'image/jpeg',
    'identification': 'image/jpeg'
  };
  
  return typeMap[docType] || 'application/pdf';
}

function generateFilePath(docType) {
  const year = new Date().getFullYear();
  const month = String(new Date().getMonth() + 1).padStart(2, '0');
  const category = docType.split('_')[0];
  return `/documents/${year}/${month}/${category}/${faker.string.alphanumeric(8)}`;
}

function generateRetentionDate() {
  const years = randomElement([7, 10, 15, 25]); // Different retention periods
  const retentionDate = new Date();
  retentionDate.setFullYear(retentionDate.getFullYear() + years);
  return retentionDate.toISOString().split('T')[0];
}

function generateDocumentTags(docType) {
  const tagMap = {
    'medical_record': ['medical', 'clinical', 'history'],
    'lab_report': ['lab', 'diagnostic', 'results'],
    'imaging_study': ['imaging', 'radiology', 'diagnostic'],
    'consent_form': ['consent', 'legal', 'signed'],
    'insurance_card': ['insurance', 'coverage', 'benefits']
  };
  
  const tags = tagMap[docType] || ['medical', 'patient'];
  return faker.helpers.arrayElements(tags, faker.number.int({ min: 1, max: 3 }));
}

function generateDocumentMetadata(docType) {
  return {
    source: randomElement(['upload', 'scan', 'import', 'fax']),
    quality: randomElement(['high', 'medium', 'low']),
    ocr_processed: faker.datatype.boolean({ probability: 0.7 }),
    page_count: faker.number.int({ min: 1, max: 10 })
  };
}

function generateAuditTrail() {
  return [
    {
      action: 'created',
      user: faker.person.fullName(),
      timestamp: faker.date.past({ days: 30 }).toISOString(),
      ip_address: faker.internet.ip()
    }
  ];
}

function addDays(dateString, days) {
  const date = new Date(dateString);
  date.setDate(date.getDate() + days);
  return date.toISOString().split('T')[0];
}

function subtractDays(dateString, days) {
  const date = new Date(dateString);
  date.setDate(date.getDate() - days);
  return date.toISOString().split('T')[0];
} 